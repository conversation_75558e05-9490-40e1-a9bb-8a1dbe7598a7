@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    嵌入完整Web文件到Go程序
echo ========================================
echo.

echo [信息] 正在读取web目录中的文件...

:: 检查web目录是否存在
if not exist "web" (
    echo [错误] web目录不存在
    pause
    exit /b 1
)

:: 创建临时的嵌入文件
echo [信息] 创建嵌入文件...

echo package main > embedded_web_full.go
echo. >> embedded_web_full.go
echo import ( >> embedded_web_full.go
echo 	"embed" >> embedded_web_full.go
echo 	"io" >> embedded_web_full.go
echo 	"io/fs" >> embedded_web_full.go
echo 	"net/http" >> embedded_web_full.go
echo 	"os" >> embedded_web_full.go
echo 	"path" >> embedded_web_full.go
echo 	"strings" >> embedded_web_full.go
echo 	"time" >> embedded_web_full.go
echo ^) >> embedded_web_full.go
echo. >> embedded_web_full.go
echo // 嵌入完整的web目录 >> embedded_web_full.go
echo //go:embed web/* >> embedded_web_full.go
echo var webFiles embed.FS >> embedded_web_full.go
echo. >> embedded_web_full.go

:: 添加文件系统函数
echo // 获取嵌入的文件系统 >> embedded_web_full.go
echo func getEmbeddedFS^(^) fs.FS { >> embedded_web_full.go
echo 	if webFS, err := fs.Sub^(webFiles, "web"^); err == nil { >> embedded_web_full.go
echo 		return webFS >> embedded_web_full.go
echo 	} >> embedded_web_full.go
echo 	return nil >> embedded_web_full.go
echo } >> embedded_web_full.go
echo. >> embedded_web_full.go

:: 添加静态文件处理器
echo // 创建静态文件处理器 >> embedded_web_full.go
echo func createStaticHandler^(^) http.Handler { >> embedded_web_full.go
echo 	fsys := getEmbeddedFS^(^) >> embedded_web_full.go
echo 	return http.HandlerFunc^(func^(w http.ResponseWriter, r *http.Request^) { >> embedded_web_full.go
echo 		urlPath := r.URL.Path >> embedded_web_full.go
echo 		if urlPath == "/" { >> embedded_web_full.go
echo 			urlPath = "/index.html" >> embedded_web_full.go
echo 		} >> embedded_web_full.go
echo 		filePath := strings.TrimPrefix^(urlPath, "/static/"^) >> embedded_web_full.go
echo 		if filePath == urlPath { >> embedded_web_full.go
echo 			filePath = strings.TrimPrefix^(urlPath, "/"^) >> embedded_web_full.go
echo 		} >> embedded_web_full.go
echo 		ext := path.Ext^(filePath^) >> embedded_web_full.go
echo 		switch ext { >> embedded_web_full.go
echo 		case ".html": >> embedded_web_full.go
echo 			w.Header^(^).Set^("Content-Type", "text/html; charset=utf-8"^) >> embedded_web_full.go
echo 		case ".css": >> embedded_web_full.go
echo 			w.Header^(^).Set^("Content-Type", "text/css; charset=utf-8"^) >> embedded_web_full.go
echo 		case ".js": >> embedded_web_full.go
echo 			w.Header^(^).Set^("Content-Type", "application/javascript; charset=utf-8"^) >> embedded_web_full.go
echo 		} >> embedded_web_full.go
echo 		file, err := fsys.Open^(filePath^) >> embedded_web_full.go
echo 		if err != nil { >> embedded_web_full.go
echo 			http.NotFound^(w, r^) >> embedded_web_full.go
echo 			return >> embedded_web_full.go
echo 		} >> embedded_web_full.go
echo 		defer file.Close^(^) >> embedded_web_full.go
echo 		stat, err := file.Stat^(^) >> embedded_web_full.go
echo 		if err != nil { >> embedded_web_full.go
echo 			http.Error^(w, "Internal Server Error", http.StatusInternalServerError^) >> embedded_web_full.go
echo 			return >> embedded_web_full.go
echo 		} >> embedded_web_full.go
echo 		http.ServeContent^(w, r, stat.Name^(^), stat.ModTime^(^), file.^(io.ReadSeeker^)^) >> embedded_web_full.go
echo 	}^) >> embedded_web_full.go
echo } >> embedded_web_full.go

echo.
echo [成功] 已创建 embedded_web_full.go
echo.
echo [信息] 现在可以使用以下命令构建：
echo go build -ldflags "-s -w" -o mahjong-helper-integrated.exe main_integrated.go embedded_web_full.go
echo.

pause
