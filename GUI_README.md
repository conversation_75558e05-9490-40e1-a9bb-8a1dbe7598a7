# 雀魂MAX独立版 - GUI界面使用说明

## 🎯 简介

这是雀魂MAX独立版的图形化界面，提供了友好的GUI来管理皮肤插件和助手插件，让用户无需手动编辑配置文件即可享受所有功能。

## 🚀 快速启动

### 推荐方式
双击 `start_gui.bat` 文件即可启动GUI界面。

### 命令行方式
```bash
python start_gui.py
```

## 📋 功能特性

### 🎮 程序控制
- **一键启动/停止**: 方便地控制代理服务的启动和停止
- **插件开关**: 可以独立控制皮肤插件和助手插件的启用状态
- **状态监控**: 实时显示程序运行状态和连接状态
- **快捷操作**: 快速打开配置目录、日志目录等

### 🎨 皮肤插件管理
- **配置保存/读取**: 支持将当前皮肤配置保存为文件，下次可以快速读取
- **可视化设置**: 通过GUI界面设置角色、皮肤、称号等，无需手动编辑YAML
- **功能开关**: 方便地开启/关闭各种功能（便捷提示、表情解锁等）
- **一键重置**: 可以快速重置配置为默认值

### 🤖 助手插件管理
- **集成模式**: 助手程序完全集成在GUI中，输出直接显示
- **传统模式**: 启动独立的助手程序窗口
- **实时分析**: 所有分析结果实时显示在GUI界面中
- **输出管理**: 支持清空、保存助手分析输出
- **连接测试**: 测试与Go助手程序的连接状态
- **配置管理**: 设置助手API地址等参数

### ⚙️ 高级设置
- **协议文件管理**: 自动更新协议文件，支持手动检查更新
- **网络设置**: 配置代理端口、SSL设置等
- **安全设置**: 控制访问权限，提高安全性
- **日志设置**: 配置日志级别和自动清理

### 📝 运行日志
- **实时日志**: 显示程序运行的详细日志信息
- **日志管理**: 支持清空、保存、刷新日志
- **自动滚动**: 可选择是否自动滚动到最新日志

## 🎯 使用流程

### 1. 基础使用
1. 启动GUI界面
2. 在"程序控制"选项卡中选择需要的插件（皮肤插件默认开启）
3. 点击"启动程序"
4. 设置浏览器代理到 `127.0.0.1:23410`
5. 启动雀魂游戏

### 2. 皮肤插件配置
1. 切换到"皮肤插件"选项卡
2. 根据需要调整各项设置（角色、皮肤、称号等）
3. 点击"保存皮肤插件设置"
4. 如果程序正在运行，选择"应用设置"会提示重启程序

### 3. 配置保存与读取
1. 在"皮肤插件"选项卡中配置好所有设置
2. 点击"保存当前配置"，选择保存位置和文件名
3. 下次使用时点击"读取配置"，选择之前保存的配置文件
4. 配置会自动应用到界面，然后保存即可

### 4. 助手插件使用

**集成模式（推荐）：**
1. 切换到"助手插件"选项卡
2. 点击"启动集成助手"
3. 助手程序在后台运行，分析结果直接显示在GUI中
4. 在"程序控制"中启用助手插件
5. 启动雀魂MAX程序
6. 所有分析信息将实时显示在助手输出区域

**传统模式：**
1. 点击"启动Go助手"打开独立助手窗口
2. 点击"测试连接"确认连接正常
3. 在"程序控制"中启用助手插件
4. 启动雀魂MAX程序

### 5. Go助手程序获取

**自动处理（推荐）**
1. 在GUI中点击"启动集成助手"或"启动Go助手"
2. 程序会自动查找可执行文件
3. 如果没有找到，会提示编译或下载

**手动编译**
1. 安装Go语言环境
2. 双击运行 `build_helper.bat`
3. 或进入 `mahjong-helper-master` 目录运行：`go build -o mahjong-helper.exe main.go`

## 📁 配置文件说明

GUI界面会自动管理以下配置文件：
- `config/settings.yaml` - 主配置文件
- `config/settings.mod.yaml` - 皮肤插件配置
- `config/settings.helper.yaml` - 助手插件配置

用户保存的配置文件为JSON格式，包含皮肤插件的所有设置。

## ⚠️ 注意事项

1. **首次运行**: 需要网络连接下载协议文件
2. **杀毒软件**: 可能需要添加程序到白名单
3. **端口占用**: 确保23410端口未被其他程序占用
4. **Go助手**: 用户端无需安装Go环境（使用预编译版本）
5. **配置备份**: 建议定期备份重要的配置文件

## 🔧 故障排除

### GUI启动失败
- 检查Python版本是否 >= 3.10
- 运行 `pip install -r requirements_fixed.txt` 安装依赖
- 确保在项目根目录运行

### 程序启动失败
- 检查端口23410是否被占用
- 查看运行日志中的错误信息
- 尝试以管理员权限运行

### 助手连接失败
- 点击"启动集成助手"或"启动Go助手"按钮
- 运行 build_helper.bat 进行编译
- 检查防火墙设置和API地址

### 配置保存失败
- 检查config目录的写入权限
- 确保磁盘空间充足
- 查看运行日志获取详细信息

## 📞 技术支持

如果遇到问题：
1. 查看"运行日志"选项卡中的错误信息
2. 检查config目录中的配置文件
3. 参考原项目的文档和说明
