package main

import (
	"bufio"
	"context"
	"embed"
	"fmt"
	"html/template"
	"io"
	"net/http"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

//go:embed web_gui_template.html
var webGUITemplate embed.FS

type WebGUI struct {
	echo       *echo.Echo
	upgrader   websocket.Upgrader
	clients    map[*websocket.Conn]bool
	clientsMux sync.RWMutex
	isRunning  bool
	cmd        *exec.Cmd
	ctx        context.Context
	cancel     context.CancelFunc
}

func NewWebGUI() *WebGUI {
	e := echo.New()
	e.HideBanner = true
	e.Use(middleware.Logger())
	e.Use(middleware.Recover())
	e.Use(middleware.CORS())

	gui := &WebGUI{
		echo:    e,
		clients: make(map[*websocket.Conn]bool),
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true
			},
		},
	}

	gui.setupRoutes()
	return gui
}

func (g *WebGUI) setupRoutes() {
	// 主页面
	g.echo.GET("/", g.handleIndex)
	
	// WebSocket连接
	g.echo.GET("/ws", g.handleWebSocket)
	
	// API路由
	g.echo.POST("/api/start", g.handleStart)
	g.echo.POST("/api/stop", g.handleStop)
	g.echo.GET("/api/status", g.handleStatus)
	g.echo.POST("/api/clear", g.handleClear)
}

func (g *WebGUI) handleIndex(c echo.Context) error {
	tmpl, err := template.ParseFS(webGUITemplate, "web_gui_template.html")
	if err != nil {
		return c.String(http.StatusInternalServerError, "Template error: "+err.Error())
	}
	
	data := map[string]interface{}{
		"Title":   "日本麻将助手 GUI",
		"Version": version,
		"Author":  "EndlessCheng",
	}
	
	return tmpl.Execute(c.Response().Writer, data)
}

func (g *WebGUI) handleWebSocket(c echo.Context) error {
	ws, err := g.upgrader.Upgrade(c.Response(), c.Request(), nil)
	if err != nil {
		return err
	}
	defer ws.Close()

	g.clientsMux.Lock()
	g.clients[ws] = true
	g.clientsMux.Unlock()

	defer func() {
		g.clientsMux.Lock()
		delete(g.clients, ws)
		g.clientsMux.Unlock()
	}()

	// 发送当前状态
	status := "stopped"
	if g.isRunning {
		status = "running"
	}
	ws.WriteJSON(map[string]interface{}{
		"type":   "status",
		"status": status,
	})

	// 保持连接
	for {
		_, _, err := ws.ReadMessage()
		if err != nil {
			break
		}
	}

	return nil
}

func (g *WebGUI) handleStart(c echo.Context) error {
	if g.isRunning {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": "助手已在运行中",
		})
	}

	go g.startHelper()
	
	return c.JSON(http.StatusOK, map[string]string{
		"message": "助手启动中...",
	})
}

func (g *WebGUI) handleStop(c echo.Context) error {
	if !g.isRunning {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": "助手未在运行",
		})
	}

	g.stopHelper()
	
	return c.JSON(http.StatusOK, map[string]string{
		"message": "助手已停止",
	})
}

func (g *WebGUI) handleStatus(c echo.Context) error {
	status := "stopped"
	if g.isRunning {
		status = "running"
	}
	
	return c.JSON(http.StatusOK, map[string]interface{}{
		"status":    status,
		"isRunning": g.isRunning,
	})
}

func (g *WebGUI) handleClear(c echo.Context) error {
	g.broadcast(map[string]interface{}{
		"type": "clear",
	})
	
	return c.JSON(http.StatusOK, map[string]string{
		"message": "日志已清空",
	})
}

func (g *WebGUI) startHelper() {
	g.isRunning = true
	g.broadcast(map[string]interface{}{
		"type":   "status",
		"status": "running",
	})
	g.broadcast(map[string]interface{}{
		"type":    "output",
		"content": "正在启动日本麻将助手...\n",
	})

	g.ctx, g.cancel = context.WithCancel(context.Background())

	// 构建命令 - 调用原始的CLI功能
	g.cmd = exec.CommandContext(g.ctx, os.Args[0], "--cli", "-majsoul")
	
	stdout, err := g.cmd.StdoutPipe()
	if err != nil {
		g.broadcast(map[string]interface{}{
			"type":    "output",
			"content": fmt.Sprintf("创建输出管道失败: %v\n", err),
		})
		g.isRunning = false
		return
	}

	stderr, err := g.cmd.StderrPipe()
	if err != nil {
		g.broadcast(map[string]interface{}{
			"type":    "output",
			"content": fmt.Sprintf("创建错误管道失败: %v\n", err),
		})
		g.isRunning = false
		return
	}

	if err := g.cmd.Start(); err != nil {
		g.broadcast(map[string]interface{}{
			"type":    "output",
			"content": fmt.Sprintf("启动失败: %v\n", err),
		})
		g.isRunning = false
		return
	}

	g.broadcast(map[string]interface{}{
		"type":    "output",
		"content": "助手已启动，等待连接...\n",
	})

	// 读取输出
	go g.readOutput(stdout, "STDOUT")
	go g.readOutput(stderr, "STDERR")

	// 等待进程结束
	err = g.cmd.Wait()
	g.isRunning = false
	g.broadcast(map[string]interface{}{
		"type":   "status",
		"status": "stopped",
	})

	if err != nil && g.ctx.Err() == nil {
		g.broadcast(map[string]interface{}{
			"type":    "output",
			"content": fmt.Sprintf("进程异常退出: %v\n", err),
		})
	}
}

func (g *WebGUI) stopHelper() {
	g.isRunning = false
	if g.cancel != nil {
		g.cancel()
	}
	if g.cmd != nil && g.cmd.Process != nil {
		g.cmd.Process.Kill()
	}
	
	g.broadcast(map[string]interface{}{
		"type":   "status",
		"status": "stopped",
	})
	g.broadcast(map[string]interface{}{
		"type":    "output",
		"content": "助手已停止\n",
	})
}

func (g *WebGUI) readOutput(reader io.Reader, prefix string) {
	scanner := bufio.NewScanner(reader)
	for scanner.Scan() {
		line := scanner.Text()
		// 过滤掉一些不必要的输出
		if strings.Contains(line, "DEBUG") {
			continue
		}
		
		g.broadcast(map[string]interface{}{
			"type":    "output",
			"content": fmt.Sprintf("[%s] %s\n", time.Now().Format("15:04:05"), line),
		})
	}
}

func (g *WebGUI) broadcast(message map[string]interface{}) {
	g.clientsMux.RLock()
	defer g.clientsMux.RUnlock()
	
	for client := range g.clients {
		err := client.WriteJSON(message)
		if err != nil {
			client.Close()
			delete(g.clients, client)
		}
	}
}

func (g *WebGUI) Start(port int) error {
	fmt.Printf("日本麻将助手 GUI 服务器启动在端口 %d\n", port)
	fmt.Printf("请在浏览器中访问: http://localhost:%d\n", port)
	
	return g.echo.Start(":" + strconv.Itoa(port))
}

func webGuiMain() {
	gui := NewWebGUI()
	
	port := 8080
	if port == 0 {
		port = 8080
	}
	
	if err := gui.Start(port); err != nil {
		fmt.Printf("启动Web GUI失败: %v\n", err)
		os.Exit(1)
	}
}
