package main

import (
	"context"
	"embed"
	"fmt"
	"html/template"
	"net/http"
	"os"
	"os/exec"
	"strconv"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

//go:embed web_gui_template.html
var webGUITemplate embed.FS

type WebGUI struct {
	echo       *echo.Echo
	upgrader   websocket.Upgrader
	clients    map[*websocket.Conn]bool
	clientsMux sync.RWMutex
	isRunning  bool
	cmd        *exec.Cmd
	ctx        context.Context
	cancel     context.CancelFunc
}

func NewWebGUI() *WebGUI {
	e := echo.New()
	e.HideBanner = true
	e.Use(middleware.Logger())
	e.Use(middleware.Recover())
	e.Use(middleware.CORS())

	gui := &WebGUI{
		echo:    e,
		clients: make(map[*websocket.Conn]bool),
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true
			},
		},
	}

	gui.setupRoutes()
	return gui
}

func (g *WebGUI) setupRoutes() {
	// 主页面
	g.echo.GET("/", g.handleIndex)

	// WebSocket连接
	g.echo.GET("/ws", g.handleWebSocket)

	// API路由
	g.echo.POST("/api/start", g.handleStart)
	g.echo.POST("/api/stop", g.handleStop)
	g.echo.GET("/api/status", g.handleStatus)
	g.echo.POST("/api/clear", g.handleClear)
}

func (g *WebGUI) handleIndex(c echo.Context) error {
	tmpl, err := template.ParseFS(webGUITemplate, "web_gui_template.html")
	if err != nil {
		return c.String(http.StatusInternalServerError, "Template error: "+err.Error())
	}

	data := map[string]interface{}{
		"Title":   "日本麻将助手 GUI",
		"Version": version,
		"Author":  "EndlessCheng",
	}

	return tmpl.Execute(c.Response().Writer, data)
}

func (g *WebGUI) handleWebSocket(c echo.Context) error {
	ws, err := g.upgrader.Upgrade(c.Response(), c.Request(), nil)
	if err != nil {
		return err
	}
	defer ws.Close()

	g.clientsMux.Lock()
	g.clients[ws] = true
	g.clientsMux.Unlock()

	defer func() {
		g.clientsMux.Lock()
		delete(g.clients, ws)
		g.clientsMux.Unlock()
	}()

	// 发送当前状态
	status := "stopped"
	if g.isRunning {
		status = "running"
	}
	ws.WriteJSON(map[string]interface{}{
		"type":   "status",
		"status": status,
	})

	// 保持连接
	for {
		_, _, err := ws.ReadMessage()
		if err != nil {
			break
		}
	}

	return nil
}

func (g *WebGUI) handleStart(c echo.Context) error {
	if g.isRunning {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": "助手已在运行中",
		})
	}

	go g.startHelper()

	return c.JSON(http.StatusOK, map[string]string{
		"message": "助手启动中...",
	})
}

func (g *WebGUI) handleStop(c echo.Context) error {
	if !g.isRunning {
		return c.JSON(http.StatusBadRequest, map[string]string{
			"error": "助手未在运行",
		})
	}

	g.stopHelper()

	return c.JSON(http.StatusOK, map[string]string{
		"message": "助手已停止",
	})
}

func (g *WebGUI) handleStatus(c echo.Context) error {
	status := "stopped"
	if g.isRunning {
		status = "running"
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"status":    status,
		"isRunning": g.isRunning,
	})
}

func (g *WebGUI) handleClear(c echo.Context) error {
	g.broadcast(map[string]interface{}{
		"type": "clear",
	})

	return c.JSON(http.StatusOK, map[string]string{
		"message": "日志已清空",
	})
}

func (g *WebGUI) startHelper() {
	g.isRunning = true
	g.broadcast(map[string]interface{}{
		"type":   "status",
		"status": "running",
	})
	g.broadcast(map[string]interface{}{
		"type":    "output",
		"content": "正在启动日本麻将助手...\n",
	})

	g.ctx, g.cancel = context.WithCancel(context.Background())

	// 直接调用内部的助手功能
	go func() {
		defer func() {
			g.isRunning = false
			g.broadcast(map[string]interface{}{
				"type":   "status",
				"status": "stopped",
			})
		}()

		g.broadcast(map[string]interface{}{
			"type":    "output",
			"content": "助手已启动，等待连接...\n",
		})

		// 模拟助手运行，实际上应该调用原始的助手逻辑
		// 这里我们启动一个简化的服务器来演示
		g.broadcast(map[string]interface{}{
			"type":    "output",
			"content": fmt.Sprintf("[%s] 日本麻将助手 %s (by EndlessCheng)\n", time.Now().Format("15:04:05"), version),
		})

		g.broadcast(map[string]interface{}{
			"type":    "output",
			"content": fmt.Sprintf("[%s] 已选择 - 雀魂\n", time.Now().Format("15:04:05")),
		})

		g.broadcast(map[string]interface{}{
			"type":    "output",
			"content": fmt.Sprintf("[%s] 启动服务器 localhost:12121\n", time.Now().Format("15:04:05")),
		})

		// 这里应该调用真正的助手服务器逻辑
		// 为了演示，我们调用原始的服务器函数
		err := runServer(true, 12121)
		if err != nil && g.ctx.Err() == nil {
			g.broadcast(map[string]interface{}{
				"type":    "output",
				"content": fmt.Sprintf("[%s] 服务器异常退出: %v\n", time.Now().Format("15:04:05"), err),
			})
		}
	}()
}

func (g *WebGUI) stopHelper() {
	g.isRunning = false
	if g.cancel != nil {
		g.cancel()
	}

	g.broadcast(map[string]interface{}{
		"type":   "status",
		"status": "stopped",
	})
	g.broadcast(map[string]interface{}{
		"type":    "output",
		"content": fmt.Sprintf("[%s] 助手已停止\n", time.Now().Format("15:04:05")),
	})
}

func (g *WebGUI) broadcast(message map[string]interface{}) {
	g.clientsMux.RLock()
	defer g.clientsMux.RUnlock()

	for client := range g.clients {
		err := client.WriteJSON(message)
		if err != nil {
			client.Close()
			delete(g.clients, client)
		}
	}
}

func (g *WebGUI) Start(port int) error {
	fmt.Printf("日本麻将助手 GUI 服务器启动在端口 %d\n", port)
	fmt.Printf("请在浏览器中访问: http://localhost:%d\n", port)

	return g.echo.Start(":" + strconv.Itoa(port))
}

func webGuiMain() {
	gui := NewWebGUI()

	port := 8080
	if port == 0 {
		port = 8080
	}

	if err := gui.Start(port); err != nil {
		fmt.Printf("启动Web GUI失败: %v\n", err)
		os.Exit(1)
	}
}
