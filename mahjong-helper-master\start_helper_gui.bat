@echo off
chcp 65001 >nul
title 日本麻将助手 - GUI启动器

echo ========================================
echo    日本麻将助手 - GUI启动器
echo    原作者：EndlessCheng
echo    GUI设计：Nfilmjon (小约)
echo ========================================
echo.

echo 正在检查Go环境...
go version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 错误：未找到Go环境！
    echo 请先安装Go 1.13或更高版本
    pause
    exit /b 1
)

echo 正在检查可执行文件...
if not exist "mahjong-helper-gui.exe" (
    echo 可执行文件不存在，正在构建...
    call build_gui.bat
    if %ERRORLEVEL% NEQ 0 (
        echo 构建失败！
        pause
        exit /b 1
    )
)

echo 启动Web GUI界面...
echo 程序将在浏览器中打开，请稍候...
echo.

start http://localhost:8080
mahjong-helper-gui.exe

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo 程序异常退出，按任意键关闭窗口...
    pause >nul
)
