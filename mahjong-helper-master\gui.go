package main

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"os"
	"os/exec"
	"strings"
	"time"

	"github.com/lxn/walk"
)

type MahjongHelperGUI struct {
	mainWindow     *walk.MainWindow
	statusLabel    *walk.Label
	startButton    *walk.PushButton
	stopButton     *walk.PushButton
	outputText     *walk.TextEdit
	clearButton    *walk.PushButton
	settingsButton *walk.PushButton

	isRunning bool
	cmd       *exec.Cmd
	ctx       context.Context
	cancel    context.CancelFunc
}

func NewMahjongHelperGUI() *MahjongHelperGUI {
	return &MahjongHelperGUI{}
}

func (g *MahjongHelperGUI) Run() error {
	var err error
	g.mainWindow, err = walk.NewMainWindow()
	if err != nil {
		return err
	}

	g.mainWindow.SetTitle("日本麻将助手 GUI")
	g.mainWindow.SetSize(walk.Size{Width: 600, Height: 700})
	g.mainWindow.SetMinMaxSize(walk.Size{Width: 600, Height: 700}, walk.Size{})

	// 创建主布局
	vbox := walk.NewVBoxLayout()
	vbox.SetMargins(walk.Margins{HNear: 20, VNear: 20, HFar: 20, VFar: 20})
	g.mainWindow.SetLayout(vbox)

	// 标题
	titleLabel, _ := walk.NewLabel(g.mainWindow)
	titleLabel.SetText("日本麻将助手")
	titleFont, _ := walk.NewFont("Microsoft YaHei", 18, walk.FontBold)
	titleLabel.SetFont(titleFont)
	titleLabel.SetTextAlignment(walk.AlignCenter)

	// 副标题
	subtitleLabel, _ := walk.NewLabel(g.mainWindow)
	subtitleLabel.SetText(fmt.Sprintf("%s by EndlessCheng | GUI设计：Nfilmjon (小约)", version))
	subtitleFont, _ := walk.NewFont("Microsoft YaHei", 10, 0)
	subtitleLabel.SetFont(subtitleFont)
	subtitleLabel.SetTextAlignment(walk.AlignCenter)

	// 分隔线
	sep1, _ := walk.NewLineEdit(g.mainWindow)
	sep1.SetText("────────────────────────────────────────────────────────────────")
	sep1.SetReadOnly(true)
	sep1.SetMinMaxSize(walk.Size{Height: 2}, walk.Size{})

	// 状态标签
	g.statusLabel, _ = walk.NewLabel(g.mainWindow)
	g.statusLabel.SetText("状态：未启动")
	statusFont, _ := walk.NewFont("Microsoft YaHei", 12, walk.FontBold)
	g.statusLabel.SetFont(statusFont)
	g.statusLabel.SetTextAlignment(walk.AlignCenter)
	g.statusLabel.SetMinMaxSize(walk.Size{Height: 40}, walk.Size{})

	// 分隔线
	sep2, _ := walk.NewLineEdit(g.mainWindow)
	sep2.SetText("────────────────────────────────────────────────────────────────")
	sep2.SetReadOnly(true)
	sep2.SetMinMaxSize(walk.Size{Height: 2}, walk.Size{})

	// 按钮容器
	buttonComposite, _ := walk.NewComposite(g.mainWindow)
	hbox := walk.NewHBoxLayout()
	hbox.SetMargins(walk.Margins{VNear: 10, VFar: 10})
	buttonComposite.SetLayout(hbox)

	// 开始按钮
	g.startButton, _ = walk.NewPushButton(buttonComposite)
	g.startButton.SetText("开始助手")
	buttonFont, _ := walk.NewFont("Microsoft YaHei", 12, walk.FontBold)
	g.startButton.SetFont(buttonFont)
	g.startButton.SetMinMaxSize(walk.Size{Height: 40}, walk.Size{})
	g.startButton.Clicked().Attach(g.onStartClicked)

	// 停止按钮
	g.stopButton, _ = walk.NewPushButton(buttonComposite)
	g.stopButton.SetText("停止助手")
	g.stopButton.SetFont(buttonFont)
	g.stopButton.SetMinMaxSize(walk.Size{Height: 40}, walk.Size{})
	g.stopButton.SetEnabled(false)
	g.stopButton.Clicked().Attach(g.onStopClicked)

	// 清空按钮
	g.clearButton, _ = walk.NewPushButton(buttonComposite)
	g.clearButton.SetText("清空日志")
	normalFont, _ := walk.NewFont("Microsoft YaHei", 12, 0)
	g.clearButton.SetFont(normalFont)
	g.clearButton.SetMinMaxSize(walk.Size{Height: 40}, walk.Size{})
	g.clearButton.Clicked().Attach(g.onClearClicked)

	// 设置按钮
	g.settingsButton, _ = walk.NewPushButton(buttonComposite)
	g.settingsButton.SetText("设置选项")
	g.settingsButton.SetFont(normalFont)
	g.settingsButton.SetMinMaxSize(walk.Size{Height: 40}, walk.Size{})
	g.settingsButton.Clicked().Attach(g.onSettingsClicked)

	// 分隔线
	sep3, _ := walk.NewLineEdit(g.mainWindow)
	sep3.SetText("────────────────────────────────────────────────────────────────")
	sep3.SetReadOnly(true)
	sep3.SetMinMaxSize(walk.Size{Height: 2}, walk.Size{})

	// 输出标签
	outputLabel, _ := walk.NewLabel(g.mainWindow)
	outputLabel.SetText("输出日志")
	outputLabelFont, _ := walk.NewFont("Microsoft YaHei", 12, walk.FontBold)
	outputLabel.SetFont(outputLabelFont)

	// 输出文本框
	g.outputText, _ = walk.NewTextEdit(g.mainWindow)
	g.outputText.SetText("等待启动...")
	g.outputText.SetReadOnly(true)
	outputFont, _ := walk.NewFont("Consolas", 10, 0)
	g.outputText.SetFont(outputFont)
	g.outputText.SetMinMaxSize(walk.Size{Height: 300}, walk.Size{})

	// 底部信息
	sep4, _ := walk.NewLineEdit(g.mainWindow)
	sep4.SetText("────────────────────────────────────────────────────────────────")
	sep4.SetReadOnly(true)
	sep4.SetMinMaxSize(walk.Size{Height: 2}, walk.Size{})

	infoLabel1, _ := walk.NewLabel(g.mainWindow)
	infoLabel1.SetText("使用说明：1. 点击\"开始助手\"启动服务 | 2. 在浏览器中访问雀魂 | 3. 开始游戏即可看到分析结果")
	infoFont, _ := walk.NewFont("Microsoft YaHei", 9, 0)
	infoLabel1.SetFont(infoFont)
	infoLabel1.SetTextAlignment(walk.AlignCenter)

	infoLabel2, _ := walk.NewLabel(g.mainWindow)
	infoLabel2.SetText("配置：支持雀魂平台 | 端口：12121 | 更多设置请查看配置文件")
	infoLabel2.SetFont(infoFont)
	infoLabel2.SetTextAlignment(walk.AlignCenter)

	// 设置关闭事件
	g.mainWindow.Closing().Attach(g.onClosing)

	// 显示窗口
	g.mainWindow.Show()
	g.mainWindow.Run()

	return nil
}

func (g *MahjongHelperGUI) onStartClicked() {
	if g.isRunning {
		return
	}

	g.isRunning = true
	g.startButton.SetEnabled(false)
	g.stopButton.SetEnabled(true)
	g.statusLabel.SetText("状态：正在启动...")
	g.outputText.SetText("正在启动日本麻将助手...\n")

	go g.startHelper()
}

func (g *MahjongHelperGUI) onStopClicked() {
	g.stopHelper()
}

func (g *MahjongHelperGUI) onClearClicked() {
	g.outputText.SetText("")
}

func (g *MahjongHelperGUI) onSettingsClicked() {
	walk.MsgBox(g.mainWindow, "设置选项",
		"配置管理功能：\n\n"+
			"• 当前版本使用默认配置\n"+
			"• 支持雀魂平台\n"+
			"• 端口：12121\n"+
			"• 更多设置请查看配置文件\n\n"+
			"使用说明：\n"+
			"1. 点击'开始助手'启动服务\n"+
			"2. 在浏览器中访问雀魂\n"+
			"3. 开始游戏即可看到分析结果",
		walk.MsgBoxIconInformation)
}

func (g *MahjongHelperGUI) startHelper() {
	defer func() {
		g.isRunning = false
		g.mainWindow.Synchronize(func() {
			g.startButton.SetEnabled(true)
			g.stopButton.SetEnabled(false)
			g.statusLabel.SetText("状态：已停止")
		})
	}()

	g.ctx, g.cancel = context.WithCancel(context.Background())

	// 构建命令 - 调用原始的CLI功能
	g.cmd = exec.CommandContext(g.ctx, os.Args[0], "-majsoul")

	stdout, err := g.cmd.StdoutPipe()
	if err != nil {
		g.appendOutput(fmt.Sprintf("创建输出管道失败: %v\n", err))
		return
	}

	stderr, err := g.cmd.StderrPipe()
	if err != nil {
		g.appendOutput(fmt.Sprintf("创建错误管道失败: %v\n", err))
		return
	}

	if err := g.cmd.Start(); err != nil {
		g.appendOutput(fmt.Sprintf("启动失败: %v\n", err))
		return
	}

	g.mainWindow.Synchronize(func() {
		g.statusLabel.SetText("状态：运行中 (端口: 12121)")
	})
	g.appendOutput("助手已启动，等待连接...\n")

	// 读取输出
	go g.readOutput(stdout, "STDOUT")
	go g.readOutput(stderr, "STDERR")

	// 等待进程结束
	err = g.cmd.Wait()
	if err != nil && g.ctx.Err() == nil {
		g.appendOutput(fmt.Sprintf("进程异常退出: %v\n", err))
	}
}

func (g *MahjongHelperGUI) stopHelper() {
	g.isRunning = false
	if g.cancel != nil {
		g.cancel()
	}
	if g.cmd != nil && g.cmd.Process != nil {
		g.cmd.Process.Kill()
	}

	g.startButton.SetEnabled(true)
	g.stopButton.SetEnabled(false)
	g.statusLabel.SetText("状态：已停止")
	g.appendOutput("助手已停止\n")
}

func (g *MahjongHelperGUI) readOutput(reader io.Reader, prefix string) {
	scanner := bufio.NewScanner(reader)
	for scanner.Scan() {
		line := scanner.Text()
		// 过滤掉一些不必要的输出
		if strings.Contains(line, "DEBUG") {
			continue
		}
		g.appendOutput(fmt.Sprintf("[%s] %s\n", time.Now().Format("15:04:05"), line))
	}
}

func (g *MahjongHelperGUI) appendOutput(text string) {
	g.mainWindow.Synchronize(func() {
		currentText := g.outputText.Text()
		g.outputText.SetText(currentText + text)
		// 滚动到底部
		g.outputText.SendMessage(0x0115, 7, 0) // WM_VSCROLL, SB_BOTTOM
	})
}

func (g *MahjongHelperGUI) onClosing(canceled *bool, reason walk.CloseReason) {
	if g.isRunning {
		result := walk.MsgBox(g.mainWindow, "确认退出", "助手正在运行中，确定要退出吗？", walk.MsgBoxYesNo|walk.MsgBoxIconQuestion)
		if result == walk.DlgCmdNo {
			*canceled = true
			return
		}
		g.stopHelper()
	}
}

func nativeGuiMain() {
	gui := NewMahjongHelperGUI()
	if err := gui.Run(); err != nil {
		fmt.Printf("启动GUI失败: %v\n", err)
		os.Exit(1)
	}
}
