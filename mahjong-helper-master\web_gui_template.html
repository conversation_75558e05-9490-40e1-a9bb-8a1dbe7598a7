<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .status-section {
            padding: 30px;
            text-align: center;
            border-bottom: 1px solid #f0f0f0;
        }

        .status-indicator {
            display: inline-block;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .status-stopped {
            background: #fee;
            color: #c53030;
            border: 2px solid #fed7d7;
        }

        .status-running {
            background: #f0fff4;
            color: #38a169;
            border: 2px solid #c6f6d5;
        }

        .controls {
            padding: 30px;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            border-bottom: 1px solid #f0f0f0;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .btn-primary {
            background: #007aff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: #ff3b30;
            color: white;
        }

        .btn-danger:hover {
            background: #d70015;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #f2f2f7;
            color: #1d1d1f;
        }

        .btn-secondary:hover {
            background: #e5e5ea;
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .output-section {
            padding: 30px;
        }

        .output-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .output-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
        }

        .output-container {
            background: #1e1e1e;
            border-radius: 12px;
            padding: 20px;
            height: 400px;
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
        }

        .output-content {
            color: #ffffff;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .settings-info {
            padding: 20px 30px;
            background: #f8f9fa;
            border-top: 1px solid #f0f0f0;
            font-size: 14px;
            color: #666;
            text-align: center;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007aff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            z-index: 1000;
        }

        .connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">连接中...</div>
    
    <div class="container">
        <div class="header">
            <h1>{{.Title}}</h1>
            <p>{{.Version}} by {{.Author}} | GUI设计：Nfilmjon (小约)</p>
        </div>

        <div class="status-section">
            <div class="status-indicator" id="statusIndicator">状态：检查中...</div>
        </div>

        <div class="controls">
            <button class="btn btn-primary" id="startBtn" onclick="startHelper()">
                <span id="startBtnText">开始助手</span>
            </button>
            <button class="btn btn-danger" id="stopBtn" onclick="stopHelper()" disabled>停止助手</button>
            <button class="btn btn-secondary" onclick="clearOutput()">清空日志</button>
            <button class="btn btn-secondary" onclick="showSettings()">设置选项</button>
        </div>

        <div class="output-section">
            <div class="output-header">
                <h3>输出日志</h3>
            </div>
            <div class="output-container">
                <div class="output-content" id="output">等待启动...</div>
            </div>
        </div>

        <div class="settings-info">
            <p><strong>使用说明：</strong></p>
            <p>1. 点击"开始助手"启动服务 | 2. 在浏览器中访问雀魂 | 3. 开始游戏即可看到分析结果</p>
            <p><strong>配置：</strong>支持雀魂平台 | 端口：12121 | 更多设置请查看配置文件</p>
        </div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        let isRunning = false;

        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function() {
                isConnected = true;
                updateConnectionStatus();
                checkStatus();
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            };
            
            ws.onclose = function() {
                isConnected = false;
                updateConnectionStatus();
                setTimeout(connectWebSocket, 3000);
            };
            
            ws.onerror = function() {
                isConnected = false;
                updateConnectionStatus();
            };
        }

        function handleWebSocketMessage(data) {
            switch(data.type) {
                case 'status':
                    updateStatus(data.status);
                    break;
                case 'output':
                    appendOutput(data.content);
                    break;
                case 'clear':
                    clearOutputContent();
                    break;
            }
        }

        function updateConnectionStatus() {
            const statusEl = document.getElementById('connectionStatus');
            if (isConnected) {
                statusEl.textContent = '已连接';
                statusEl.className = 'connection-status connected';
            } else {
                statusEl.textContent = '连接断开';
                statusEl.className = 'connection-status disconnected';
            }
        }

        function updateStatus(status) {
            const indicator = document.getElementById('statusIndicator');
            const startBtn = document.getElementById('startBtn');
            const stopBtn = document.getElementById('stopBtn');
            const startBtnText = document.getElementById('startBtnText');
            
            isRunning = status === 'running';
            
            if (isRunning) {
                indicator.textContent = '状态：运行中 (端口: 12121)';
                indicator.className = 'status-indicator status-running';
                startBtn.disabled = true;
                stopBtn.disabled = false;
                startBtnText.innerHTML = '<span class="loading"></span>运行中';
            } else {
                indicator.textContent = '状态：未启动';
                indicator.className = 'status-indicator status-stopped';
                startBtn.disabled = false;
                stopBtn.disabled = true;
                startBtnText.textContent = '开始助手';
            }
        }

        function appendOutput(content) {
            const output = document.getElementById('output');
            output.textContent += content;
            output.scrollTop = output.scrollHeight;
        }

        function clearOutputContent() {
            document.getElementById('output').textContent = '';
        }

        async function startHelper() {
            try {
                const response = await fetch('/api/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                const result = await response.json();
                if (!response.ok) {
                    alert('启动失败: ' + result.error);
                }
            } catch (error) {
                alert('启动失败: ' + error.message);
            }
        }

        async function stopHelper() {
            try {
                const response = await fetch('/api/stop', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
                
                const result = await response.json();
                if (!response.ok) {
                    alert('停止失败: ' + result.error);
                }
            } catch (error) {
                alert('停止失败: ' + error.message);
            }
        }

        async function clearOutput() {
            try {
                await fetch('/api/clear', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });
            } catch (error) {
                console.error('清空日志失败:', error);
            }
        }

        function showSettings() {
            alert('设置选项：\n\n' +
                  '• 当前版本使用默认配置\n' +
                  '• 支持雀魂平台\n' +
                  '• 端口：12121\n' +
                  '• 更多设置请查看配置文件\n\n' +
                  '使用说明：\n' +
                  '1. 点击"开始助手"启动服务\n' +
                  '2. 在浏览器中访问雀魂\n' +
                  '3. 开始游戏即可看到分析结果');
        }

        async function checkStatus() {
            try {
                const response = await fetch('/api/status');
                const result = await response.json();
                updateStatus(result.status);
            } catch (error) {
                console.error('检查状态失败:', error);
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            connectWebSocket();
        });
    </script>
</body>
</html>
