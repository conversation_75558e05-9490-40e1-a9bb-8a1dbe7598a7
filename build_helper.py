#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Go助手程序编译脚本
用于预编译Go助手程序，避免用户需要安装Go环境
"""

import os
import subprocess
import sys
from pathlib import Path

def check_go_environment():
    """检查Go环境"""
    try:
        result = subprocess.run(["go", "version"], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✓ Go环境检测成功: {result.stdout.strip()}")
            return True
        else:
            print("✗ Go环境检测失败")
            return False
    except FileNotFoundError:
        print("✗ 未找到Go环境")
        return False
    except Exception as e:
        print(f"✗ Go环境检测出错: {e}")
        return False

def build_helper():
    """编译Go助手程序"""
    helper_dir = Path("./mahjong-helper-master")
    if not helper_dir.exists():
        print("✗ 找不到mahjong-helper-master目录")
        return False
        
    main_go = helper_dir / "main.go"
    if not main_go.exists():
        print("✗ 找不到main.go文件")
        return False
        
    print("开始编译Go助手程序...")
    
    try:
        # Windows版本
        if os.name == 'nt':
            output_file = helper_dir / "mahjong-helper.exe"
            cmd = ["go", "build", "-o", "mahjong-helper.exe", "main.go"]
        else:
            output_file = helper_dir / "mahjong-helper"
            cmd = ["go", "build", "-o", "mahjong-helper", "main.go"]
            
        print(f"编译命令: {' '.join(cmd)}")
        print("编译中，请稍候...")
        
        result = subprocess.run(cmd, 
                              cwd=str(helper_dir),
                              capture_output=True, 
                              text=True, 
                              timeout=120)
        
        if result.returncode == 0:
            if output_file.exists():
                print(f"✓ 编译成功: {output_file}")
                print(f"文件大小: {output_file.stat().st_size / 1024 / 1024:.1f} MB")
                return True
            else:
                print("✗ 编译完成但找不到输出文件")
                return False
        else:
            print("✗ 编译失败:")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            if result.stdout:
                print(f"输出信息: {result.stdout}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ 编译超时")
        return False
    except Exception as e:
        print(f"✗ 编译过程中发生错误: {e}")
        return False

def test_helper():
    """测试编译好的助手程序"""
    helper_dir = Path("./mahjong-helper-master")
    
    if os.name == 'nt':
        helper_exe = helper_dir / "mahjong-helper.exe"
    else:
        helper_exe = helper_dir / "mahjong-helper"
        
    if not helper_exe.exists():
        print("✗ 找不到编译好的助手程序")
        return False
        
    try:
        print("测试助手程序...")
        # 测试程序是否能正常启动（显示版本信息后退出）
        result = subprocess.run([str(helper_exe), "-h"], 
                              capture_output=True, 
                              text=True, 
                              timeout=10)
        
        if "日本麻将助手" in result.stdout or "mahjong-helper" in result.stdout.lower():
            print("✓ 助手程序测试成功")
            return True
        else:
            print("✗ 助手程序测试失败")
            print(f"输出: {result.stdout}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ 助手程序测试超时")
        return False
    except Exception as e:
        print(f"✗ 测试助手程序时发生错误: {e}")
        return False

def main():
    """主函数"""
    print("Go助手程序编译脚本")
    print("=" * 40)
    
    # 检查Go环境
    if not check_go_environment():
        print("\n请先安装Go语言环境:")
        print("1. 访问 https://golang.org/dl/")
        print("2. 下载并安装适合您系统的Go版本")
        print("3. 重启命令行后再运行此脚本")
        return 1
        
    # 编译助手程序
    if not build_helper():
        print("\n编译失败，请检查错误信息")
        return 1
        
    # 测试助手程序
    if not test_helper():
        print("\n测试失败，编译的程序可能有问题")
        return 1
        
    print("\n✅ Go助手程序编译完成！")
    print("\n现在可以使用GUI界面启动助手，无需安装Go环境。")
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n编译被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n编译过程中发生未知错误: {e}")
        sys.exit(1)
