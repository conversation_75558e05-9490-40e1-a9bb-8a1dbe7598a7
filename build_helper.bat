@echo off
chcp 65001 >nul
title Go助手程序编译脚本

echo.
echo ========================================
echo Go助手程序编译脚本
echo ========================================
echo.

:: 检查Go环境
echo 检查Go语言环境...
go version >nul 2>&1
if errorlevel 1 (
    echo ✗ 未找到Go语言环境
    echo.
    echo 请先安装Go语言环境:
    echo 1. 访问 https://golang.org/dl/
    echo 2. 下载并安装适合您系统的Go版本
    echo 3. 重启命令行后再运行此脚本
    echo.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('go version') do set GO_VERSION=%%i
echo ✓ Go环境检测成功: %GO_VERSION%

:: 检查目录结构
if not exist "mahjong-helper-master" (
    echo ✗ 找不到mahjong-helper-master目录
    pause
    exit /b 1
)

if not exist "mahjong-helper-master\main.go" (
    echo ✗ 找不到main.go文件
    pause
    exit /b 1
)

:: 编译Go助手程序
echo.
echo 开始编译Go助手程序...
cd mahjong-helper-master

echo 编译命令: go build -o mahjong-helper.exe main.go
echo 编译中，请稍候...

go build -o mahjong-helper.exe main.go

if errorlevel 1 (
    echo ✗ 编译失败
    cd ..
    pause
    exit /b 1
)

if not exist "mahjong-helper.exe" (
    echo ✗ 编译完成但找不到输出文件
    cd ..
    pause
    exit /b 1
)

echo ✓ 编译成功: mahjong-helper.exe

:: 显示文件信息
for %%A in (mahjong-helper.exe) do (
    set /a SIZE=%%~zA/1024/1024
    echo 文件大小: !SIZE! MB
)

:: 测试程序
echo.
echo 测试助手程序...
mahjong-helper.exe -h >nul 2>&1
if errorlevel 1 (
    echo ! 程序测试可能有问题，但文件已生成
) else (
    echo ✓ 助手程序测试成功
)

cd ..

echo.
echo ✅ Go助手程序编译完成！
echo.
echo 现在可以使用GUI界面启动助手，无需安装Go环境。
echo 编译好的文件位置: mahjong-helper-master\mahjong-helper.exe
echo.
pause
