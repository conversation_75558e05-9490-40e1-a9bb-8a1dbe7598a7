# 雀魂MAX独立版 - 新GUI界面使用说明

本项目为雀魂MAX独立版和日本麻将助手添加了现代化的GUI界面，采用iOS设计风格，简洁美观。

## 功能特性

### Python GUI (雀魂MAX独立版)
- 🎨 **现代化设计**：采用iOS设计风格，界面简洁干净
- 🚀 **一键启动**：点击"开始加载"即可启动服务
- ⚙️ **配置管理**：支持配置文件的保存、读取和重置
- 📊 **实时状态**：显示当前运行状态和配置信息
- 🔧 **设置选项**：简化的设置界面，只包含必要功能

### Golang Web GUI (日本麻将助手)
- 🎯 **统一风格**：与Python GUI保持相同的iOS设计风格
- 🌐 **Web界面**：基于浏览器的现代化界面，无需安装额外依赖
- 📝 **实时日志**：通过WebSocket实时显示助手运行输出
- 🎮 **简单操作**：一键启动/停止助手服务
- 💾 **日志管理**：支持清空日志功能
- 🔄 **完整功能**：保持原有助手的所有分析功能

## 安装依赖

### Python GUI 依赖
```bash
pip install -r requirements_fixed.txt
```

主要依赖：
- `customtkinter==5.2.2` - 现代化UI框架
- `pillow==10.4.0` - 图像处理库

### Golang Web GUI 依赖
```bash
cd mahjong-helper-master
go mod download
```

主要依赖：
- `github.com/labstack/echo/v4` - Web框架
- `github.com/gorilla/websocket` - WebSocket支持
- 无需CGO，纯Go实现

## 使用方法

### 启动Python GUI
```bash
# 默认启动GUI模式
python main.py

# 强制启动命令行模式
python main.py --cli
```

### 启动Golang Web GUI
```bash
cd mahjong-helper-master

# 构建Web GUI版本
./build_gui.bat  # Windows
./build_gui.sh   # Linux/macOS

# 运行Web GUI版本
./mahjong-helper-gui      # 启动Web服务器，然后访问 http://localhost:8080
./mahjong-helper-gui --cli # 命令行模式
```

## 界面说明

### Python GUI 界面
1. **主标题区域**：显示程序名称和作者信息
2. **状态显示**：显示当前服务运行状态
3. **开始加载按钮**：启动/停止雀魂MAX服务
4. **设置选项按钮**：打开配置管理窗口
5. **配置信息区域**：显示当前配置状态

### 设置选项窗口
- **保存当前配置**：将当前配置保存到指定文件
- **读取配置文件**：从文件加载配置并应用
- **重置为默认**：恢复默认配置设置

### Golang Web GUI 界面
1. **标题区域**：显示助手名称、版本和作者信息
2. **状态显示**：实时显示助手运行状态
3. **控制按钮区域**：
   - 开始助手/停止助手按钮
   - 清空日志按钮
   - 设置选项按钮
4. **输出日志区域**：通过WebSocket实时显示助手输出
5. **连接状态指示器**：显示与服务器的连接状态
6. **使用说明区域**：显示配置信息和使用指南

## 配置文件

### Python配置 (config/settings.yaml)
```yaml
plugin_enable:
  mod: true      # MOD功能开关
  helper: false  # Helper功能开关
liqi:
  auto_update: true           # 自动更新开关
  github_token: ''           # GitHub令牌
  liqi_version: 'v0.11.107.w' # Liqi版本
```

### Golang配置
助手使用默认配置，支持雀魂平台，端口12121。

## 注意事项

1. **首次运行**：Python GUI首次运行时会创建默认配置
2. **依赖安装**：确保已安装所有必要依赖
3. **防火墙设置**：可能需要允许程序通过防火墙
4. **端口占用**：
   - Python服务使用端口23410
   - Golang助手使用端口12121
   - Golang Web GUI使用端口8080
5. **兼容性**：
   - Python GUI需要图形环境支持
   - Golang Web GUI只需要浏览器支持

## 故障排除

### Python GUI 问题
- **导入错误**：检查是否安装了customtkinter和pillow
- **启动失败**：查看终端错误信息，可能是端口被占用
- **配置错误**：尝试重置为默认配置

### Golang Web GUI 问题
- **编译失败**：确保Go环境正确安装（需要Go 1.16+）
- **依赖错误**：运行`go mod download`下载依赖
- **端口占用**：确保8080端口未被占用
- **浏览器访问失败**：检查防火墙设置
- **WebSocket连接失败**：确保浏览器支持WebSocket

## 技术特性

### 设计风格
- 采用iOS设计语言
- 圆角按钮和卡片式布局
- 现代化颜色搭配
- 响应式交互效果

### 架构特点
- Python GUI基于customtkinter
- Golang GUI基于fyne框架
- 支持GUI/CLI双模式
- 异步处理和多线程支持

## 更新日志

### v1.0.0
- 初始版本发布
- 实现Python和Golang双GUI界面
- 支持配置管理功能
- 采用iOS设计风格

## 致谢

- 原作者：Avenshy (雀魂MAX)
- 原作者：EndlessCheng (日本麻将助手)
- GUI设计：Nfilmjon (小约)

## 许可证

本项目遵循原项目的开源许可证。
