syntax = "proto3";

package lq;

option go_package = ".;majprotocol";

message NotifyRoomGameStart {

    string game_url = 1;
    string connect_token = 2;
    string game_uuid = 3;
    string location = 4;
}

message NotifyMatchGameStart {

    string game_url = 1;
    string connect_token = 2;
    string game_uuid = 3;
    uint32 match_mode_id = 4;
    string location = 5;
}

message NotifyRoomPlayerReady {

    uint32 account_id = 1;
    bool ready = 2;
    AccountReadyState account_list = 3;
    uint32 seq = 4;

    message AccountReadyState {

        uint32 account_id = 1;
        bool ready = 2;
    }
}

message NotifyRoomPlayerDressing {

    uint32 account_id = 1;
    bool dressing = 2;
    AccountDressingState account_list = 3;
    uint32 seq = 4;

    message AccountDressingState {

        uint32 account_id = 1;
        bool dressing = 2;
    }
}

message NotifyRoomPlayerUpdate {

    uint32 owner_id = 3;
    uint32 robot_count = 4;
    repeated PlayerGameView player_list = 5;
    uint32 seq = 6;
    repeated PlayerGameView robots = 7;
    repeated uint32 positions = 8;
}

message NotifyRoomKickOut {
}

message NotifyFriendStateChange {

    uint32 target_id = 1;
    AccountActiveState active_state = 2;
}

message NotifyFriendViewChange {

    uint32 target_id = 1;
    PlayerBaseView base = 2;
}

message NotifyFriendChange {

    uint32 account_id = 1;
    uint32 type = 2;
    Friend friend = 3;
}

message NotifyNewFriendApply {

    uint32 account_id = 1;
    uint32 apply_time = 2;
    uint32 removed_id = 3;
}

message NotifyClientMessage {

    PlayerBaseView sender = 1;
    uint32 type = 2;
    string content = 3;
}

message NotifyAccountUpdate {

    AccountUpdate update = 1;
}

message NotifyAnotherLogin {
}

message NotifyAccountLogout {
}

message NotifyAnnouncementUpdate {

    repeated AnnouncementUpdate update_list = 1;

    message AnnouncementUpdate {

        string lang = 1;
        string platform = 2;
    }
}

message NotifyNewMail {

    Mail mail = 1;
}

message NotifyDeleteMail {

    repeated uint32 mail_id_list = 1;
}

message NotifyReviveCoinUpdate {

    bool has_gained = 1;
}

message NotifyDailyTaskUpdate {

    repeated TaskProgress progresses = 1;
    uint32 max_daily_task_count = 2;
    uint32 refresh_count = 3;
}

message NotifyActivityTaskUpdate {

    repeated TaskProgress progresses = 1;
}

message NotifyActivityPeriodTaskUpdate {

    repeated TaskProgress progresses = 1;
}

message NotifyAccountRandomTaskUpdate {

    repeated TaskProgress progresses = 1;
}

message NotifyActivitySegmentTaskUpdate {

    repeated lq.SegmentTaskProgress progresses = 1;
}

message NotifyActivityUpdate {

    repeated FeedActivityData list = 1;

    message FeedActivityData {

        uint32 activity_id = 1;
        uint32 feed_count = 2;
        CountWithTimeData friend_receive_data = 3;
        CountWithTimeData friend_send_data = 4;
        repeated GiftBoxData gift_inbox = 5;

        message CountWithTimeData {

            uint32 count = 1;
            uint32 last_update_time = 2;
        }

        message GiftBoxData {

            uint32 id = 1;
            uint32 item_id = 2;
            uint32 count = 3;
            uint32 from_account_id = 4;
            uint32 time = 5;
            uint32 received = 6;
        }
    }
}

message NotifyAccountChallengeTaskUpdate {

    repeated TaskProgress progresses = 1;
    uint32 level = 2;
    uint32 refresh_count = 3;
    uint32 match_count = 4;
    uint32 ticket_id = 5;
    repeated uint32 rewarded_season = 6;
}

message NotifyNewComment {
}

message NotifyRollingNotice {
}

message NotifyMaintainNotice {
}

message NotifyGiftSendRefresh {
}

message NotifyShopUpdate {

    ShopInfo shop_info = 1;
}

message NotifyIntervalUpdate {
}

message NotifyVipLevelChange {

    uint32 gift_limit = 1;
    uint32 friend_max_count = 2;
    uint32 zhp_free_refresh_limit = 3;
    uint32 zhp_cost_refresh_limit = 4;
    float buddy_bonus = 5;
    uint32 record_collect_limit = 6;
}

message NotifyServerSetting {

    ServerSettings settings = 1;
}

message NotifyPayResult {

    uint32 pay_result = 1;
    string order_id = 2;
    uint32 goods_id = 3;
    uint32 new_month_ticket = 4;
    repeated ResourceModify resource_modify = 5;

    message ResourceModify {

        uint32 id = 1;
        uint32 count = 2;
        uint32 final = 3;
    }
}

message NotifyCustomContestAccountMsg {

    uint32 unique_id = 1;
    uint32 account_id = 2;
    string sender = 3;
    string content = 4;
    uint32 verified = 5;
}

message NotifyCustomContestSystemMsg {

    uint32 unique_id = 1;
    uint32 type = 2;
    string uuid = 3;
    CustomizedContestGameStart game_start = 4;
    CustomizedContestGameEnd game_end = 5;
}

message NotifyMatchTimeout {

    string sid = 1;
}

message NotifyMatchFailed {

    string sid = 1;
}

message NotifyCustomContestState {

    uint32 unique_id = 1;
    uint32 state = 2;
}

message NotifyActivityChange {

    repeated Activity new_activities = 1;
    repeated uint32 end_activities = 2;
}

message NotifyAFKResult {

    uint32 type = 1;
    uint32 ban_end_time = 2;
    string game_uuid = 3;
}

message NotifyLoginQueueFinished {
}

message NotifyGameFinishRewardV2 {

    uint32 mode_id = 1;
    LevelChange level_change = 2;
    MatchChest match_chest = 3;
    MainCharacter main_character = 4;
    CharacterGift character_gift = 5;
    repeated BadgeAchieveProgress badges = 6;

    message LevelChange {

        AccountLevel origin = 1;
        AccountLevel final = 2;
        uint32 type = 3;
    }

    message MatchChest {

        uint32 chest_id = 1;
        uint32 origin = 2;
        uint32 final = 3;
        bool is_graded = 4;
        repeated RewardSlot rewards = 5;
    }

    message MainCharacter {

        uint32 level = 1;
        uint32 exp = 2;
        uint32 add = 3;
    }

    message CharacterGift {

        uint32 origin = 1;
        uint32 final = 2;
        uint32 add = 3;
        bool is_graded = 4;
    }
}

message NotifyActivityRewardV2 {

    repeated ActivityReward activity_reward = 1;

    message ActivityReward {

        uint32 activity_id = 1;
        repeated RewardSlot rewards = 2;
    }
}

message NotifyActivityPointV2 {

    repeated ActivityPoint activity_points = 1;

    message ActivityPoint {

        uint32 activity_id = 1;
        uint32 point = 2;
    }
}

message NotifyLeaderboardPointV2 {

    repeated LeaderboardPoint leaderboard_points = 1;

    message LeaderboardPoint {

        uint32 leaderboard_id = 1;
        uint32 point = 2;
    }
}

message NotifySeerReport {

    SeerBrief report = 1;
}

message NotifyConnectionShutdown {

    uint32 reason = 1;
    uint32 close_at = 2;
}

message Error {

    uint32 code = 1;
    repeated uint32 u32_params = 2;
    repeated string str_params = 3;
    string json_param = 4;
}

message Wrapper {

    string name = 1;
    bytes data = 2;
}

message NetworkEndpoint {

    string family = 1;
    string address = 2;
    uint32 port = 3;
}

message ReqCommon {
}

message ResCommon {

    Error error = 1;
}

message ResAccountUpdate {

    Error error = 1;
    AccountUpdate update = 2;
}

message AntiAddiction {

    uint32 online_duration = 1;
}

message HighestHuRecord {

    uint32 fanshu = 1;
    uint32 doranum = 2;
    string title = 3;
    repeated string hands = 4;
    repeated string ming = 5;
    string hupai = 6;
    uint32 title_id = 7;
}

message AccountMahjongStatistic {

    repeated uint32 final_position_counts = 1;
    RoundSummary recent_round = 2;
    HuSummary recent_hu = 3;
    HighestHuRecord highest_hu = 4;
    Liqi20Summary recent_20_hu_summary = 6;
    LiQi10Summary recent_10_hu_summary = 7;
    repeated GameResult recent_10_game_result = 8;

    message RoundSummary {

        uint32 total_count = 1;
        uint32 rong_count = 2;
        uint32 zimo_count = 3;
        uint32 fangchong_count = 4;
    }

    message HuSummary {

        uint32 total_count = 1;
        uint32 dora_round_count = 2;
        uint32 total_fan = 3;
    }

    message Liqi20Summary {

        uint32 total_count = 1;
        uint32 total_lidora_count = 2;
        uint32 average_hu_point = 3;
    }

    message LiQi10Summary {

        uint32 total_xuanshang = 1;
        uint32 total_fanshu = 2;
    }

    message GameResult {

        uint32 rank = 1;
        int32 final_point = 2;
    }
}

message AccountStatisticData {

    uint32 mahjong_category = 1;
    uint32 game_category = 2;
    AccountMahjongStatistic statistic = 3;
    uint32 game_type = 4;
}

message AccountLevel {

    uint32 id = 1;
    uint32 score = 2;
}

message ViewSlot {

    uint32 slot = 1;
    uint32 item_id = 2;
    uint32 type = 3;
    repeated uint32 item_id_list = 4;
}

message FavoriteHu {

    uint32 category = 1;
    uint32 type = 2;
    HighestHuRecord hu = 3;
    uint32 mode = 4;
}

message Account {

    uint32 account_id = 1;
    string nickname = 2;
    uint32 login_time = 3;
    uint32 logout_time = 4;
    uint32 room_id = 5;
    AntiAddiction anti_addiction = 6;
    uint32 title = 7;
    string signature = 8;
    string email = 9;
    uint32 email_verify = 10;
    uint32 gold = 11;
    uint32 diamond = 12;
    uint32 avatar_id = 13;
    uint32 vip = 14;
    int32 birthday = 15;
    string phone = 16;
    uint32 phone_verify = 17;
    repeated PlatformDiamond platform_diamond = 18;
    AccountLevel level = 21;
    AccountLevel level3 = 22;
    uint32 avatar_frame = 23;
    uint32 skin_ticket = 24;
    repeated PlatformSkinTicket platform_skin_ticket = 25;
    uint32 verified = 26;
    repeated ChallengeLevel challenge_levels = 27;
    uint32 frozen_state = 29;
    repeated AchievementCount achievement_count = 28;
    repeated uint32 loading_image = 30;
    repeated FavoriteHu favorite_hu = 34;
    repeated Badge badges = 35;

    message PlatformDiamond {

        uint32 id = 1;
        uint32 count = 2;
    }

    message PlatformSkinTicket {

        uint32 id = 1;
        uint32 count = 2;
    }

    message ChallengeLevel {

        uint32 season = 1;
        uint32 level = 2;
        uint32 rank = 3;
    }

    message AchievementCount {

        uint32 rare = 1;
        uint32 count = 2;
    }

    message Badge {

        uint32 id = 1;
        uint32 achieved_time = 2;
        uint32 achieved_counter = 3;
    }
}

message AccountOwnerData {

    repeated uint32 unlock_characters = 1;
}

message AccountUpdate {

    repeated NumericalUpdate numerical = 1;
    CharacterUpdate character = 2;
    BagUpdate bag = 3;
    AchievementUpdate achievement = 4;
    AccountShiLian shilian = 5;
    DailyTaskUpdate daily_task = 6;
    TitleUpdate title = 7;
    repeated uint32 new_recharged_list = 8;
    TaskUpdate activity_task = 9;
    TaskUpdate activity_flip_task = 10;
    TaskUpdate activity_period_task = 11;
    TaskUpdate activity_random_task = 12;
    AccountChallengeUpdate challenge = 13;
    AccountABMatchUpdate ab_match = 14;
    lq.AccountActivityUpdate activity = 15;
    SegmentTaskUpdate activity_segment_task = 16;
    MonthTicketUpdate month_ticket = 17;
    MainCharacterUpdate main_character = 18;
    BadgeUpdate badge = 19;

    message NumericalUpdate {

        uint32 id = 1;
        uint32 final = 3;
    }

    message CharacterUpdate {

        repeated Character characters = 2;
        repeated uint32 skins = 3;
        repeated uint32 finished_endings = 4;
        repeated uint32 rewarded_endings = 5;
    }

    message AchievementUpdate {

        repeated AchievementProgress progresses = 1;
        repeated uint32 rewarded_group = 2;
    }

    message DailyTaskUpdate {

        repeated TaskProgress progresses = 1;
        repeated uint32 task_list = 2;
    }

    message TitleUpdate {

        repeated uint32 new_titles = 1;
        repeated uint32 remove_titles = 2;
    }

    message TaskUpdate {

        repeated TaskProgress progresses = 1;
        repeated uint32 task_list = 2;
    }

    message AccountChallengeUpdate {

        repeated TaskProgress progresses = 1;
        uint32 level = 2;
        uint32 refresh_count = 3;
        uint32 match_count = 4;
        uint32 ticket_id = 5;
        repeated uint32 task_list = 6;
        repeated uint32 rewarded_season = 7;
    }

    message AccountABMatchUpdate {

        uint32 match_id = 1;
        uint32 match_count = 2;
        uint32 buy_in_count = 3;
        uint32 point = 4;
        bool rewarded = 5;
        repeated MatchPoint match_max_point = 6;
        bool quit = 7;

        message MatchPoint {

            uint32 match_id = 1;
            uint32 point = 2;
        }
    }

    message SegmentTaskUpdate {

        repeated lq.SegmentTaskProgress progresses = 1;
        repeated uint32 task_list = 2;
    }

    message MonthTicketUpdate {

        uint32 end_time = 1;
        uint32 last_pay_time = 2;
    }

    message MainCharacterUpdate {

        uint32 character_id = 1;
        uint32 skin_id = 2;
    }

    message BadgeUpdate {

        repeated BadgeAchieveProgress progresses = 1;
    }
}

message GameMetaData {

    uint32 room_id = 1;
    uint32 mode_id = 2;
    uint32 contest_uid = 3;
}

message AccountPlayingGame {

    string game_uuid = 1;
    uint32 category = 2;
    GameMetaData meta = 3;
}

message RandomCharacter {

    uint32 character_id = 1;
    uint32 skin_id = 2;
}

message AccountCacheView {

    uint32 cache_version = 1;
    uint32 account_id = 2;
    string nickname = 3;
    uint32 login_time = 4;
    uint32 logout_time = 5;
    bool is_online = 6;
    uint32 room_id = 7;
    uint32 title = 8;
    uint32 avatar_id = 9;
    uint32 vip = 10;
    AccountLevel level = 11;
    AccountPlayingGame playing_game = 12;
    AccountLevel level3 = 13;
    uint32 avatar_frame = 14;
    uint32 verified = 15;
    uint32 ban_deadline = 16;
    uint32 comment_ban = 17;
    uint32 ban_state = 18;
}

message PlayerBaseView {

    uint32 account_id = 1;
    uint32 avatar_id = 2;
    uint32 title = 3;
    string nickname = 4;
    AccountLevel level = 5;
    AccountLevel level3 = 6;
    uint32 avatar_frame = 7;
    uint32 verified = 8;
    uint32 is_banned = 9;
}

message PlayerGameView {

    uint32 account_id = 1;
    uint32 avatar_id = 2;
    uint32 title = 3;
    string nickname = 4;
    AccountLevel level = 5;
    Character character = 6;
    AccountLevel level3 = 7;
    uint32 avatar_frame = 8;
    uint32 verified = 9;
    repeated ViewSlot views = 10;
}

message GameSetting {

    uint32 emoji_switch = 1;
}

message GameMode {

    uint32 mode = 1;
    bool ai = 4;
    string extendinfo = 5;
    GameDetailRule detail_rule = 6;
    GameTestingEnvironmentSet testing_environment = 7;
    GameSetting game_setting = 8;
}

message GameTestingEnvironmentSet {

    uint32 paixing = 1;
    uint32 left_count = 2;
    uint32 field_spell_var = 3;
}

message GameDetailRule {

    uint32 time_fixed = 1;
    uint32 time_add = 2;
    uint32 dora_count = 3;
    uint32 shiduan = 4;
    uint32 init_point = 5;
    uint32 fandian = 6;
    bool can_jifei = 7;
    uint32 tianbian_value = 8;
    uint32 liqibang_value = 9;
    uint32 changbang_value = 10;
    uint32 noting_fafu_1 = 11;
    uint32 noting_fafu_2 = 12;
    uint32 noting_fafu_3 = 13;
    bool have_liujumanguan = 14;
    bool have_qieshangmanguan = 15;
    bool have_biao_dora = 16;
    bool have_gang_biao_dora = 17;
    bool ming_dora_immediately_open = 18;
    bool have_li_dora = 19;
    bool have_gang_li_dora = 20;
    bool have_sifenglianda = 21;
    bool have_sigangsanle = 22;
    bool have_sijializhi = 23;
    bool have_jiuzhongjiupai = 24;
    bool have_sanjiahele = 25;
    bool have_toutiao = 26;
    bool have_helelianzhuang = 27;
    bool have_helezhongju = 28;
    bool have_tingpailianzhuang = 29;
    bool have_tingpaizhongju = 30;
    bool have_yifa = 31;
    bool have_nanruxiru = 32;
    uint32 jingsuanyuandian = 33;
    int32 shunweima_2 = 34;
    int32 shunweima_3 = 35;
    int32 shunweima_4 = 36;
    bool bianjietishi = 37;
    uint32 ai_level = 38;
    bool have_zimosun = 39;
    bool disable_multi_yukaman = 40;
    uint32 fanfu = 41;
    uint32 guyi_mode = 42;
    uint32 dora3_mode = 43;
    uint32 begin_open_mode = 44;
    uint32 jiuchao_mode = 45;
    uint32 muyu_mode = 46;
    uint32 open_hand = 47;
    uint32 xuezhandaodi = 48;
    uint32 huansanzhang = 49;
    uint32 chuanma = 50;
    uint32 reveal_discard = 51;
    uint32 field_spell_mode = 52;
    uint32 zhanxing = 53;
    uint32 tianming_mode = 54;
    bool disable_leijiyiman = 60;
    uint32 disable_double_yakuman = 62;
    uint32 disable_composite_yakuman = 63;
    uint32 enable_shiti = 64;
    uint32 enable_nontsumo_liqi = 65;
    uint32 disable_double_wind_four_fu = 66;
    uint32 disable_angang_guoshi = 67;
    uint32 enable_renhe = 68;
    uint32 enable_baopai_extend_settings = 69;
    uint32 yongchang_mode = 70;
    uint32 hunzhiyiji_mode = 71;
    uint32 wanxiangxiuluo_mode = 72;
    uint32 beishuizhizhan_mode = 73;
}

message Room {

    uint32 room_id = 1;
    uint32 owner_id = 2;
    GameMode mode = 3;
    uint32 max_player_count = 4;
    repeated PlayerGameView persons = 5;
    repeated uint32 ready_list = 6;
    bool is_playing = 7;
    bool public_live = 8;
    uint32 robot_count = 9;
    uint32 tournament_id = 10;
    uint32 seq = 11;
    string pre_rule = 12;
    repeated PlayerGameView robots = 13;
    repeated uint32 positions = 14;
}

message GameEndResult {

    repeated PlayerItem players = 1;

    message PlayerItem {

        uint32 seat = 1;
        int32 total_point = 2;
        int32 part_point_1 = 3;
        int32 part_point_2 = 4;
        int32 grading_score = 5;
        int32 gold = 6;
    }
}

message GameConnectInfo {

    string connect_token = 2;
    string game_uuid = 3;
    string location = 4;
}

message ItemGainRecord {

    uint32 item_id = 1;
    uint32 count = 2;
}

message ItemGainRecords {

    uint32 record_time = 1;
    uint32 limit_source_id = 2;
    repeated ItemGainRecord records = 3;
}

message FakeRandomRecords {

    uint32 item_id = 1;
    uint32 special_item_id = 2;
    uint32 gain_count = 3;
    repeated uint32 gain_history = 4;
}

message Item {

    uint32 item_id = 1;
    uint32 stack = 2;
}

message Bag {

    repeated Item items = 1;
    repeated ItemGainRecords daily_gain_record = 2;
}

message BagUpdate {

    repeated Item update_items = 1;
    repeated ItemGainRecords update_daily_gain_record = 2;
}

message RewardSlot {

    uint32 id = 1;
    uint32 count = 2;
}

message OpenResult {

    RewardSlot reward = 1;
    RewardSlot replace = 2;
}

message RewardPlusResult {

    uint32 id = 1;
    uint32 count = 2;
    Exchange exchange = 3;

    message Exchange {

        uint32 id = 1;
        uint32 count = 2;
        uint32 exchange = 3;
    }
}

message ExecuteReward {

    RewardSlot reward = 1;
    RewardSlot replace = 2;
    uint32 replace_count = 3;
}

message ExecuteResult {

    uint32 id = 1;
    int32 count = 2;
}

message I18nContext {

    string lang = 1;
    string context = 2;
}

message Mail {

    uint32 mail_id = 1;
    uint32 state = 2;
    bool take_attachment = 3;
    string title = 4;
    string content = 5;
    repeated RewardSlot attachments = 6;
    uint32 create_time = 7;
    uint32 expire_time = 8;
    uint32 reference_id = 9;
    repeated I18nContext title_i18n = 10;
    repeated I18nContext content_i18n = 11;
    uint32 template_id = 12;
}

message AchievementProgress {

    uint32 id = 1;
    uint32 counter = 2;
    bool achieved = 3;
    bool rewarded = 4;
    uint32 achieved_time = 5;
}

message BadgeAchieveProgress {

    uint32 id = 1;
    uint32 counter = 2;
    uint32 achieved_counter = 3;
    uint32 achieved_time = 4;
}

message AccountStatisticByGameMode {

    uint32 mode = 1;
    uint32 game_count_sum = 2;
    repeated uint32 game_final_position = 3;
    uint32 fly_count = 4;
    float gold_earn_sum = 5;
    uint32 round_count_sum = 6;
    float dadian_sum = 7;
    repeated RoundEndData round_end = 8;
    uint32 ming_count_sum = 9;
    uint32 liqi_count_sum = 10;
    uint32 xun_count_sum = 11;
    uint32 highest_lianzhuang = 12;
    uint32 score_earn_sum = 13;
    repeated RankScore rank_score = 14;

    message RoundEndData {

        uint32 type = 1;
        uint32 sum = 2;
    }

    message RankScore {

        uint32 rank = 1;
        int32 score_sum = 2;
        uint32 count = 3;
    }
}

message AccountStatisticByFan {

    uint32 fan_id = 1;
    uint32 sum = 2;
}

message AccountFanAchieved {

    uint32 mahjong_category = 1;
    repeated AccountStatisticByFan fan = 2;
    uint32 liujumanguan = 3;
}

message AccountDetailStatistic {

    repeated AccountStatisticByGameMode game_mode = 1;
    repeated AccountStatisticByFan fan = 2;
    uint32 liujumanguan = 3;
    repeated AccountFanAchieved fan_achieved = 4;
}

message AccountDetailStatisticByCategory {

    uint32 category = 1;
    AccountDetailStatistic detail_statistic = 2;
}

message AccountDetailStatisticV2 {

    AccountDetailStatistic friend_room_statistic = 1;
    RankStatistic rank_statistic = 2;
    CustomizedContestStatistic customized_contest_statistic = 3;
    AccountDetailStatistic leisure_match_statistic = 4;
    ChallengeStatistic challenge_match_statistic = 5;
    AccountDetailStatistic activity_match_statistic = 6;
    AccountDetailStatistic ab_match_statistic = 7;

    message RankStatistic {

        RankData total_statistic = 1;
        RankData month_statistic = 2;
        uint32 month_refresh_time = 3;

        message RankData {

            AccountDetailStatistic all_level_statistic = 1;
            repeated RankLevelData level_data_list = 2;

            message RankLevelData {

                uint32 rank_level = 1;
                AccountDetailStatistic statistic = 2;
            }
        }
    }

    message CustomizedContestStatistic {

        AccountDetailStatistic total_statistic = 1;
        AccountDetailStatistic month_statistic = 2;
        uint32 month_refresh_time = 3;
    }

    message ChallengeStatistic {

        AccountDetailStatistic all_season = 1;
        repeated SeasonData season_data_list = 2;

        message SeasonData {

            uint32 season_id = 1;
            AccountDetailStatistic statistic = 2;
        }
    }
}

message AccountShiLian {

    uint32 step = 1;
    uint32 state = 2;
}

message ClientDeviceInfo {

    string platform = 1;
    string hardware = 2;
    string os = 3;
    string os_version = 4;
    bool is_browser = 5;
    string software = 6;
    string sale_platform = 7;
    string hardware_vendor = 8;
    string model_number = 9;
    uint32 screen_width = 10;
    uint32 screen_height = 11;
    string user_agent = 12;
    uint32 screen_type = 13;
}

message ClientVersionInfo {

    string resource = 1;
    string package = 2;
}

enum GamePlayerState {

    NULL = 0;
    AUTH = 1;
    SYNCING = 2;
    READY = 3;
}

message Announcement {

    uint32 id = 1;
    string title = 2;
    string content = 3;
    string header_image = 4;
}

message TaskProgress {

    uint32 id = 1;
    uint32 counter = 2;
    bool achieved = 3;
    bool rewarded = 4;
    bool failed = 5;
    uint32 rewarded_time = 6;
}

message GameConfig {

    uint32 category = 1;
    GameMode mode = 2;
    GameMetaData meta = 3;
}

message RPGState {

    uint32 player_damaged = 1;
    uint32 monster_damaged = 2;
    uint32 monster_seq = 3;
}

message RPGActivity {

    uint32 activity_id = 1;
    string last_show_uuid = 5;
    string last_played_uuid = 6;
    RPGState current_state = 7;
    RPGState last_show_state = 8;
    repeated uint32 received_rewards = 9;
    uint32 last_show_id = 10;
}

message ActivityArenaData {

    uint32 win_count = 1;
    uint32 lose_count = 2;
    uint32 activity_id = 3;
    uint32 enter_time = 4;
    uint32 daily_enter_count = 5;
    uint32 daily_enter_time = 6;
    uint32 max_win_count = 7;
    uint32 total_win_count = 8;
}

message FeedActivityData {

    uint32 activity_id = 1;
    uint32 feed_count = 2;
    CountWithTimeData friend_receive_data = 3;
    CountWithTimeData friend_send_data = 4;
    repeated GiftBoxData gift_inbox = 5;

    message CountWithTimeData {

        uint32 count = 1;
        uint32 last_update_time = 2;
    }

    message GiftBoxData {

        uint32 id = 1;
        uint32 item_id = 2;
        uint32 count = 3;
        uint32 from_account_id = 4;
        uint32 time = 5;
        uint32 received = 6;
    }
}

message SegmentTaskProgress {

    uint32 id = 1;
    uint32 counter = 2;
    bool achieved = 3;
    bool rewarded = 4;
    bool failed = 5;
    uint32 reward_count = 6;
    uint32 achieved_count = 7;
}

message MineActivityData {

    repeated Point dig_point = 1;
    repeated MineReward map = 2;
    uint32 id = 3;
}

message AccountActivityUpdate {

    repeated lq.MineActivityData mine_data = 1;
    repeated lq.RPGActivity rpg_data = 2;
    repeated ActivityFeedData feed_data = 3;
    repeated lq.ActivitySpotData spot_data = 4;
    repeated lq.ActivityFriendGiftData friend_gift_data = 5;
    repeated lq.ActivityUpgradeData upgrade_data = 6;
    repeated lq.ActivityGachaUpdateData gacha_data = 7;
    repeated lq.ActivitySimulationData simulation_data = 8;
    repeated ActivityCombiningLQData combining_data = 9;
    repeated lq.ActivityVillageData village_data = 10;
    repeated lq.ActivityFestivalData festival_data = 11;
    repeated lq.ActivityIslandData island_data = 12;
    repeated lq.ActivityStoryData story_data = 14;
    repeated lq.ActivityChooseUpData choose_up_data = 15;
    repeated lq.SimulationV2Data simulation_v2_data = 16;
}

message ActivityCombiningWorkbench {

    uint32 craft_id = 1;
    uint32 pos = 2;
}

message ActivityCombiningMenuData {

    uint32 menu_group = 1;
    repeated MenuRequire generated = 2;
    repeated MenuRequire multi_generated = 3;

    message MenuRequire {

        uint32 level = 1;
        uint32 count = 2;
    }
}

message ActivityCombiningOrderData {

    uint32 id = 1;
    uint32 pos = 2;
    uint32 unlock_day = 4;
    uint32 char_id = 5;
    repeated uint32 finished_craft_id = 6;
    repeated uint32 craft_id = 7;
}

message ActivityCombiningLQData {

    uint32 activity_id = 1;
    repeated ActivityCombiningWorkbench workbench = 2;
    repeated ActivityCombiningOrderData orders = 3;
    ActivityCombiningWorkbench recycle_bin = 4;
    repeated uint32 unlocked_craft = 5;
    uint32 daily_bonus_count = 6;
}

message ActivityCombiningPoolData {

    uint32 group = 1;
    uint32 count = 2;
}

message ActivityCombiningData {

    uint32 activity_id = 1;
    repeated ActivityCombiningWorkbench workbench = 2;
    repeated ActivityCombiningOrderData orders = 3;
    ActivityCombiningWorkbench recycle_bin = 4;
    ActivityCombiningMenuData menu = 5;
    uint32 current_order_id = 6;
    BonusData bonus = 7;
    repeated uint32 unlocked_craft = 8;
    repeated ActivityCombiningPoolData craft_pool = 9;
    repeated ActivityCombiningPoolData order_pool = 10;

    message BonusData {

        uint32 count = 1;
        uint32 update_time = 2;
    }
}

message VillageReward {

    uint32 id = 1;
    uint32 count = 2;
}

message VillageBuildingData {

    uint32 id = 1;
    repeated VillageReward reward = 3;
    repeated uint32 workers = 4;
}

message VillageTripData {

    uint32 start_round = 1;
    uint32 dest_id = 2;
    repeated VillageReward reward = 3;
    uint32 level = 4;
    VillageTargetInfo info = 5;
}

message VillageTaskData {

    uint32 id = 1;
    uint32 completed_count = 2;
}

message VillageTargetInfo {

    string nickname = 1;
    uint32 avatar = 2;
    uint32 avatar_frame = 3;
    uint32 title = 4;
    uint32 verified = 5;
}

message ActivityVillageData {

    uint32 activity_id = 1;
    repeated VillageBuildingData buildings = 2;
    repeated VillageTripData trip = 3;
    repeated VillageTaskData tasks = 6;
    uint32 round = 7;
}

message TimeCounterData {

    uint32 count = 1;
    uint32 update_time = 2;
}

message SignedTimeCounterData {

    int32 count = 1;
    uint32 update_time = 2;
}

message FestivalProposalData {

    uint32 id = 1;
    uint32 proposal_id = 2;
    uint32 pos = 3;
}

message ActivityFestivalData {

    uint32 activity_id = 1;
    uint32 level = 2;
    repeated FestivalProposalData proposal_list = 3;
    repeated uint32 event_list = 4;
    SignedTimeCounterData buy_record = 5;
}

message SimulationV2Data {

    uint32 activity_id = 1;
    lq.SimulationV2SeasonData season = 2;
    int32 highest_score = 3;
    lq.SimulationV2Ability upgrade = 4;
    repeated uint32 event_pool = 5;
    uint32 season_count = 6;
}

message IslandBagItemData {

    uint32 id = 1;
    repeated uint32 pos = 2;
    uint32 rotate = 3;
    uint32 goods_id = 4;
    uint32 price = 5;
}

message IslandBagData {

    uint32 id = 1;
    string matrix = 2;
    repeated IslandBagItemData items = 3;
}

message IslandGoodsData {

    uint32 goods_id = 1;
    int32 count = 2;
    uint32 update_time = 3;
}

message IslandZoneData {

    uint32 id = 1;
    SignedTimeCounterData currency_used = 2;
    repeated IslandGoodsData goods_records = 3;
}

message ActivityIslandData {

    uint32 activity_id = 1;
    uint32 zone = 2;
    repeated IslandBagData bags = 3;
    repeated IslandZoneData zones = 4;
}

message AmuletEffectData {

    uint32 id = 1;
    uint32 uid = 2;
    repeated int64 store = 3;
}

message AmuletBuffData {

    uint32 id = 1;
    repeated int64 store = 3;
}

message AmuletGameShopGoods {

    uint32 id = 1;
    bool sold = 2;
    uint32 goods_id = 3;
    uint32 price = 4;
}

message AmuletActivityTingInfo {

    string tile = 1;
    uint64 fan = 2;
    string ting_tile = 3;
}

message AmuletShowDesktopTileData {

    uint32 id = 1;
    uint32 pos = 2;
}

message AmuletGameOperation {

    uint32 type = 1;
    repeated GangTiles gang = 2;
    uint32 effect_id = 3;

    message GangTiles {

        repeated uint32 tiles = 1;
    }
}

message AmuletGameShopData {

    repeated AmuletGameShopGoods goods = 1;
    repeated uint32 effect_list = 2;
    uint32 shop_refresh_count = 3;
    uint32 refresh_price = 4;
    uint32 next_goods_id = 5;
}

message AmuletGameUpdateData {

    repeated AmuletTile tile_replace = 1;
    repeated string tian_dora = 2;
    repeated uint32 dora = 4;
    repeated uint32 hands = 7;
    repeated AmuletMingInfo ming = 8;
    repeated AmuletEffectData effect_list = 9;
    repeated AmuletEffectData buff_list = 10;
    string point = 13;
    uint32 coin = 14;
    uint32 stage = 22;
    uint32 desktop_remain = 26;
    repeated AmuletShowDesktopTileData show_desktop_tiles = 28;
    repeated AmuletActivityTingInfo ting_list = 30;
    repeated AmuletGameOperation next_operation = 31;
    repeated uint32 used_desktop = 34;
    ActivityAmuletHuRecord highest_hu = 35;
    ActivityAmuletRecord records = 36;
    repeated uint32 reward_pack = 37;
    repeated uint32 reward_effect = 38;
    repeated AmuletGameTileScoreData tile_score = 43;
    uint32 reward_pack_id = 47;
}

message AmuletGameRecordData {

    uint32 key = 1;
    int32 int_value = 2;
    string str_value = 3;
    repeated int32 int_arr_value = 4;
}

message AmuletGameTileScoreData {

    string tile = 1;
    string score = 2;
}

message AmuletGameData {

    repeated AmuletTile pool = 1;
    repeated AmuletTile tile_replace = 2;
    repeated string tian_dora = 3;
    repeated uint32 mountain = 4;
    repeated uint32 dora = 5;
    repeated uint32 hands = 7;
    repeated AmuletMingInfo ming = 8;
    repeated AmuletEffectData effect_list = 9;
    repeated AmuletBuffData buff_list = 10;
    uint32 level = 11;
    string point = 13;
    uint32 coin = 14;
    AmuletGameShopData shop = 16;
    repeated uint32 used = 20;
    repeated uint32 boss_buff = 21;
    uint32 stage = 22;
    repeated uint32 desktop = 24;
    repeated uint32 show_desktop = 25;
    uint32 desktop_remain = 26;
    repeated uint32 free_effect_list = 27;
    repeated AmuletShowDesktopTileData show_desktop_tiles = 28;
    uint32 change_tile_count = 29;
    repeated AmuletActivityTingInfo ting_list = 30;
    repeated AmuletGameOperation next_operation = 31;
    repeated AmuletBuffData shop_buff_list = 32;
    int32 remain_change_tile_count = 33;
    repeated uint32 used_desktop = 34;
    uint32 after_gang = 35;
    repeated AmuletGameRecordData record_data = 36;
    repeated AmuletBuffData skill_buff_list = 37;
    uint32 max_effect_count = 38;
    ActivityAmuletHuRecord highest_hu = 39;
    uint32 total_consumed_coin = 40;
    repeated uint32 boss_buff_id = 41;
    repeated uint32 locked_tile = 42;
    repeated AmuletGameTileScoreData tile_score = 43;
    uint32 locked_tile_count = 44;
    repeated uint32 reward_pack = 45;
    repeated uint32 reward_effect = 46;
    uint32 reward_pack_id = 47;
    uint32 total_change_tile_count = 48;
}

message ActivityAmuletUpdateData {

    uint32 activity_id = 1;
    AmuletGameUpdateData game_update = 2;
    bool game_empty = 3;
}

message AmuletSkillData {

    uint32 id = 1;
    uint32 level = 2;
}

message ActivityAmuletUpgradeData {

    repeated AmuletSkillData skill = 2;
}

message ActivityAmuletRecord {

    uint32 effect_gain_count = 1;
    uint32 hu_count = 2;
}

message ActivityAmuletHuRecord {

    string point = 1;
    string pai = 2;
    string fan = 3;
    string base = 4;
}

message ActivityAmuletIllustratedBookData {

    repeated uint32 effect_collection = 1;
    ActivityAmuletHuRecord highest_hu = 2;
    uint32 highest_level = 3;
}

message ActivityAmuletTaskData {

    repeated lq.TaskProgress progress = 1;
}

message ActivityAmuletData {

    uint32 activity_id = 1;
    AmuletGameData game = 2;
    uint32 version = 3;
    ActivityAmuletUpgradeData upgrade = 4;
    ActivityAmuletIllustratedBookData illustrated_book = 5;
    ActivityAmuletTaskData task = 6;
}

message ActivityFeedData {

    uint32 activity_id = 1;
    uint32 feed_count = 2;
    CountWithTimeData friend_receive_data = 3;
    CountWithTimeData friend_send_data = 4;
    repeated GiftBoxData gift_inbox = 5;
    uint32 max_inbox_id = 6;

    message CountWithTimeData {

        uint32 count = 1;
        uint32 last_update_time = 2;
    }

    message GiftBoxData {

        uint32 id = 1;
        uint32 item_id = 2;
        uint32 count = 3;
        uint32 from_account_id = 4;
        uint32 time = 5;
        uint32 received = 6;
    }
}

message UnlockedStoryData {

    uint32 story_id = 1;
    repeated uint32 finished_ending = 2;
    repeated uint32 rewarded_ending = 3;
    uint32 finish_rewarded = 4;
    uint32 all_finish_rewarded = 5;
}

message ActivityStoryData {

    uint32 activity_id = 1;
    repeated UnlockedStoryData unlocked_story = 2;
}

message ActivityChooseUpData {

    uint32 activity_id = 1;
    uint32 chest_id = 2;
    uint32 selection = 3;
    uint32 is_end = 4;
}

message ActivityFriendGiftData {

    uint32 activity_id = 1;
    uint32 max_inbox_id = 2;
    CountWithTimeData receive_data = 3;
    CountWithTimeData send_data = 4;
    repeated GiftBoxData gift_inbox = 5;

    message CountWithTimeData {

        uint32 count = 1;
        uint32 last_update_time = 2;
        repeated uint32 send_friend_id = 3;
    }

    message GiftBoxData {

        uint32 id = 1;
        uint32 item_id = 2;
        uint32 count = 3;
        uint32 from_account_id = 4;
        uint32 time = 5;
        uint32 received = 6;
    }
}

message ActivityUpgradeData {

    uint32 activity_id = 1;
    repeated LevelGroup groups = 2;
    uint32 received_level = 3;

    message LevelGroup {

        uint32 group_id = 1;
        uint32 level = 2;
    }
}

message GachaRecord {

    uint32 id = 1;
    uint32 count = 2;
}

message ActivityGachaData {

    uint32 activity_id = 1;
    repeated GachaRecord gained = 2;
}

message ActivityGachaUpdateData {

    uint32 activity_id = 1;
    repeated GachaRecord gained = 2;
    uint32 remain_count = 3;
}

message ActivitySimulationGameRecordMessage {

    uint32 type = 1;
    repeated uint32 args = 2;
    uint32 xun = 3;
}

message ActivitySimulationGameRecord {

    uint32 round = 1;
    repeated uint32 seats = 2;
    string uuid = 3;
    uint32 start_time = 4;
    repeated int32 scores = 5;
    repeated ActivitySimulationGameRecordMessage messages = 6;
}

message ActivitySimulationDailyContest {

    uint32 day = 1;
    repeated uint32 characters = 2;
    repeated ActivitySimulationGameRecord records = 3;
    uint32 round = 4;
}

message ActivitySimulationTrainRecord {

    uint32 time = 1;
    repeated int32 modify_stats = 2;
    repeated uint32 final_stats = 3;
    uint32 type = 4;
}

message ActivitySimulationData {

    uint32 activity_id = 1;
    repeated uint32 stats = 2;
    uint32 stamina_update_time = 3;
    repeated ActivitySimulationDailyContest daily_contest = 4;
    repeated ActivitySimulationTrainRecord train_records = 5;
}

message ActivitySpotData {

    uint32 activity_id = 1;
    repeated SpotData spots = 3;

    message SpotData {

        uint32 unique_id = 1;
        uint32 rewarded = 2;
        repeated uint32 unlocked_ending = 3;
        uint32 unlocked = 4;
    }
}

message AccountActiveState {

    uint32 account_id = 1;
    uint32 login_time = 2;
    uint32 logout_time = 3;
    bool is_online = 4;
    AccountPlayingGame playing = 5;
}

message Friend {

    PlayerBaseView base = 1;
    AccountActiveState state = 2;
    string remark = 3;
}

message Point {

    uint32 x = 1;
    uint32 y = 2;
}

message MineReward {

    Point point = 1;
    uint32 reward_id = 2;
    bool received = 3;
}

message GameLiveUnit {

    uint32 timestamp = 1;
    uint32 action_category = 2;
    bytes action_data = 3;
}

message GameLiveSegment {

    repeated GameLiveUnit actions = 1;
}

message GameLiveSegmentUri {

    uint32 segment_id = 1;
    string segment_uri = 2;
}

message GameLiveHead {

    string uuid = 1;
    uint32 start_time = 2;
    GameConfig game_config = 3;
    repeated PlayerGameView players = 4;
    repeated uint32 seat_list = 5;
}

message GameNewRoundState {

    repeated uint32 seat_states = 1;
}

message GameEndAction {

    uint32 state = 1;
}

message GameNoopAction {
}

message CommentItem {

    uint32 comment_id = 1;
    uint32 timestamp = 2;
    PlayerBaseView commenter = 3;
    string content = 4;
    uint32 is_banned = 5;
}

message RollingNotice {

    string content = 2;
    uint32 start_time = 3;
    uint32 end_time = 4;
    uint32 repeat_interval = 5;
    repeated uint32 repeat_time = 7;
    uint32 repeat_type = 8;
}

message MaintainNotice {

    uint32 maintain_time = 1;
}

message BillingGoods {

    string id = 1;
    string name = 2;
    string desc = 3;
    string icon = 4;
    uint32 resource_id = 5;
    uint32 resource_count = 6;
}

message BillShortcut {

    uint32 id = 1;
    uint32 count = 2;
    uint32 deal_price = 3;
}

message BillingProduct {

    BillingGoods goods = 1;
    string currency_code = 2;
    uint32 currency_price = 3;
    uint32 sort_weight = 4;
}

message Character {

    uint32 charid = 1;
    uint32 level = 2;
    uint32 exp = 3;
    repeated ViewSlot views = 4;
    uint32 skin = 5;
    bool is_upgraded = 6;
    repeated uint32 extra_emoji = 7;
    repeated uint32 rewarded_level = 8;
}

message BuyRecord {

    uint32 id = 1;
    uint32 count = 2;
}

message ZHPShop {

    repeated uint32 goods = 1;
    repeated BuyRecord buy_records = 2;
    RefreshCount free_refresh = 3;
    RefreshCount cost_refresh = 4;

    message RefreshCount {

        uint32 count = 1;
        uint32 limit = 2;
    }
}

message MonthTicketInfo {

    uint32 id = 1;
    uint32 end_time = 2;
    uint32 last_pay_time = 3;
}

message ShopInfo {

    ZHPShop zhp = 1;
    repeated BuyRecord buy_records = 2;
    uint32 last_refresh_time = 3;
}

message ChangeNicknameRecord {

    string from = 1;
    string to = 2;
    uint32 time = 3;
}

message ServerSettings {

    PaymentSetting payment_setting = 3;
    PaymentSettingV2 payment_setting_v2 = 4;
    NicknameSetting nickname_setting = 5;
}

message NicknameSetting {

    uint32 enable = 1;
    repeated string nicknames = 2;
}

message PaymentSettingV2 {

    uint32 open_payment = 1;
    repeated PaymentSettingUnit payment_platforms = 2;

    message PaymentMaintain {

        uint32 start_time = 1;
        uint32 end_time = 2;
        uint32 goods_click_action = 3;
        string goods_click_text = 4;
        repeated string enabled_channel = 5;
    }

    message PaymentSettingUnit {

        string platform = 1;
        bool is_show = 2;
        uint32 goods_click_action = 3;
        string goods_click_text = 4;
        PaymentMaintain maintain = 5;
        bool enable_for_frozen_account = 6;
        string extra_data = 7;
        repeated string enabled_channel = 8;
    }
}

message PaymentSetting {

    uint32 open_payment = 1;
    uint32 payment_info_show_type = 2;
    string payment_info = 3;
    WechatData wechat = 4;
    AlipayData alipay = 5;

    message WechatData {

        bool disable_create = 1;
        uint32 payment_source_platform = 2;
        bool enable_credit = 3;
    }

    message AlipayData {

        bool disable_create = 1;
        uint32 payment_source_platform = 2;
    }
}

message AccountSetting {

    uint32 key = 1;
    uint32 value = 2;
}

message ChestData {

    uint32 chest_id = 1;
    uint32 total_open_count = 2;
    uint32 consume_count = 3;
    uint32 face_black_count = 4;
}

message ChestDataV2 {

    uint32 chest_id = 1;
    uint32 total_open_count = 2;
    uint32 face_black_count = 3;
    uint32 ticket_face_black_count = 4;
}

message FaithData {

    uint32 faith_id = 1;
    uint32 total_open_count = 2;
    uint32 consume_count = 3;
    int32 modify_count = 4;
}

message CustomizedContestBase {

    uint32 unique_id = 1;
    uint32 contest_id = 2;
    string contest_name = 3;
    uint32 state = 4;
    uint32 creator_id = 5;
    uint32 create_time = 6;
    uint32 start_time = 7;
    uint32 finish_time = 8;
    bool open = 9;
    uint32 contest_type = 10;
    string public_notice = 11;
    uint32 check_state = 12;
    string checking_name = 13;
}

message CustomizedContestExtend {

    uint32 unique_id = 1;
    string public_notice = 2;
}

message CustomizedContestAbstract {

    uint32 unique_id = 1;
    uint32 contest_id = 2;
    string contest_name = 3;
    uint32 state = 4;
    uint32 creator_id = 5;
    uint32 create_time = 6;
    uint32 start_time = 7;
    uint32 finish_time = 8;
    bool open = 9;
    string public_notice = 10;
    uint32 contest_type = 11;
}

message CustomizedContestDetail {

    uint32 unique_id = 1;
    uint32 contest_id = 2;
    string contest_name = 3;
    uint32 state = 4;
    uint32 creator_id = 5;
    uint32 create_time = 6;
    uint32 start_time = 7;
    uint32 finish_time = 8;
    bool open = 9;
    uint32 rank_rule = 10;
    GameMode game_mode = 11;
    string private_notice = 12;
    uint32 observer_switch = 13;
    uint32 emoji_switch = 14;
    uint32 contest_type = 15;
    uint32 disable_broadcast = 16;
    uint32 signup_start_time = 17;
    uint32 signup_end_time = 18;
    uint32 signup_type = 19;
    uint32 auto_match = 20;
}

message CustomizedContestPlayerReport {

    uint32 rank_rule = 1;
    uint32 rank = 2;
    int32 point = 3;
    repeated uint32 game_ranks = 4;
    uint32 total_game_count = 5;
}

message RecordGame {

    string uuid = 1;
    uint32 start_time = 2;
    uint32 end_time = 3;
    GameConfig config = 5;
    repeated AccountInfo accounts = 11;
    GameEndResult result = 12;
    repeated AccountInfo robots = 13;
    uint32 standard_rule = 14;

    message AccountInfo {

        uint32 account_id = 1;
        uint32 seat = 2;
        string nickname = 3;
        uint32 avatar_id = 4;
        Character character = 5;
        uint32 title = 6;
        AccountLevel level = 7;
        AccountLevel level3 = 8;
        uint32 avatar_frame = 9;
        uint32 verified = 10;
        repeated ViewSlot views = 11;
    }
}

message RecordListEntry {

    uint32 version = 1;
    string uuid = 2;
    uint32 start_time = 3;
    uint32 end_time = 4;
    uint32 tag = 5;
    uint32 subtag = 6;
    repeated RecordPlayerResult players = 7;
    uint32 standard_rule = 8;
}

message RecordPlayerResult {

    uint32 rank = 1;
    uint32 account_id = 2;
    string nickname = 3;
    AccountLevel level = 4;
    AccountLevel level3 = 5;
    uint32 seat = 6;
    int32 pt = 7;
    int32 point = 8;
    uint32 max_hu_type = 9;
    uint32 action_liqi = 10;
    uint32 action_rong = 11;
    uint32 action_zimo = 12;
    uint32 action_chong = 13;
    uint32 verified = 14;
}

message CustomizedContestGameStart {

    repeated Item players = 1;

    message Item {

        uint32 account_id = 1;
        string nickname = 2;
    }
}

message CustomizedContestGameEnd {

    repeated Item players = 1;

    message Item {

        uint32 account_id = 1;
        string nickname = 2;
        int32 total_point = 3;
    }
}

message Activity {

    uint32 activity_id = 1;
    uint32 start_time = 2;
    uint32 end_time = 3;
    string type = 4;
}

message ExchangeRecord {

    uint32 exchange_id = 1;
    uint32 count = 2;
}

message ActivityAccumulatedPointData {

    uint32 activity_id = 1;
    int32 point = 2;
    repeated uint32 gained_reward_list = 3;
}

message ActivityRankPointData {

    uint32 leaderboard_id = 1;
    int32 point = 2;
    bool gained_reward = 3;
    uint32 gainable_time = 4;
}

message GameRoundHuData {

    HuPai hupai = 1;
    repeated Fan fans = 2;
    uint32 score = 3;
    uint32 xun = 4;
    uint32 title_id = 5;
    uint32 fan_sum = 6;
    uint32 fu_sum = 7;
    uint32 yakuman_count = 8;
    uint32 biao_dora_count = 9;
    uint32 red_dora_count = 10;
    uint32 li_dora_count = 11;
    uint32 babei_count = 12;
    uint32 xuan_shang_count = 13;
    uint32 pai_left_count = 14;

    message HuPai {

        string tile = 1;
        uint32 seat = 2;
        uint32 liqi = 3;
    }

    message Fan {

        uint32 id = 1;
        uint32 count = 2;
        uint32 fan = 3;
    }
}

message GameRoundPlayerFangChongInfo {

    uint32 seat = 1;
    string tile = 2;
    uint32 pai_left_count = 3;
}

message GameRoundPlayerResult {

    uint32 type = 1;
    repeated string hands = 2;
    repeated string ming = 3;
    uint32 liqi_type = 4;
    bool is_fulu = 5;
    bool is_liujumanguan = 6;
    uint32 lian_zhuang = 7;
    GameRoundHuData hu = 8;
    repeated GameRoundPlayerFangChongInfo fangchongs = 9;
    bool liqi_fangchong = 10;
    bool liqi_failed = 11;
}

message GameRoundPlayer {

    int32 score = 1;
    uint32 rank = 2;
    GameRoundPlayerResult result = 3;
}

message GameRoundSnapshot {

    uint32 ju = 1;
    uint32 ben = 2;
    repeated GameRoundPlayer players = 3;
}

message GameFinalSnapshot {

    string uuid = 1;
    uint32 state = 2;
    uint32 category = 3;
    GameMode mode = 4;
    GameMetaData meta = 5;
    CalculateParam calculate_param = 6;
    uint32 create_time = 7;
    uint32 start_time = 8;
    uint32 finish_time = 9;
    repeated GameSeat seats = 10;
    repeated GameRoundSnapshot rounds = 11;
    repeated PlayerGameView account_views = 12;
    repeated FinalPlayer final_players = 13;
    repeated AFKInfo afk_info = 14;
    repeated PlayerGameView robot_views = 15;

    message CalculateParam {

        uint32 init_point = 1;
        uint32 jingsuanyuandian = 2;
        repeated int32 rank_points = 3;
    }

    message GameSeat {

        uint32 type = 1;
        uint32 account_id = 2;
        NetworkEndpoint notify_endpoint = 3;
        string client_address = 4;
        bool is_connected = 5;
    }

    message FinalPlayer {

        uint32 seat = 1;
        int32 total_point = 2;
        int32 part_point_1 = 3;
        int32 part_point_2 = 4;
        int32 grading_score = 5;
        int32 gold = 6;
    }

    message AFKInfo {

        uint32 deal_tile_count = 1;
        uint32 moqie_count = 2;
        uint32 seat = 3;
    }
}

message RecordCollectedData {

    string uuid = 1;
    string remarks = 2;
    uint32 start_time = 3;
    uint32 end_time = 4;
}

message ContestDetailRule {

    uint32 init_point = 5;
    uint32 fandian = 6;
    bool can_jifei = 7;
    uint32 tianbian_value = 8;
    uint32 liqibang_value = 9;
    uint32 changbang_value = 10;
    uint32 noting_fafu_1 = 11;
    uint32 noting_fafu_2 = 12;
    uint32 noting_fafu_3 = 13;
    bool have_liujumanguan = 14;
    bool have_qieshangmanguan = 15;
    bool have_biao_dora = 16;
    bool have_gang_biao_dora = 17;
    bool ming_dora_immediately_open = 18;
    bool have_li_dora = 19;
    bool have_gang_li_dora = 20;
    bool have_sifenglianda = 21;
    bool have_sigangsanle = 22;
    bool have_sijializhi = 23;
    bool have_jiuzhongjiupai = 24;
    bool have_sanjiahele = 25;
    bool have_toutiao = 26;
    bool have_helelianzhuang = 27;
    bool have_helezhongju = 28;
    bool have_tingpailianzhuang = 29;
    bool have_tingpaizhongju = 30;
    bool have_yifa = 31;
    bool have_nanruxiru = 32;
    uint32 jingsuanyuandian = 33;
    int32 shunweima_2 = 34;
    int32 shunweima_3 = 35;
    int32 shunweima_4 = 36;
    bool bianjietishi = 37;
    uint32 ai_level = 38;
    bool have_zimosun = 39;
    bool disable_multi_yukaman = 40;
    uint32 guyi_mode = 41;
    bool disable_leijiyiman = 42;
    uint32 dora3_mode = 43;
    uint32 xuezhandaodi = 44;
    uint32 huansanzhang = 45;
    uint32 chuanma = 46;
    uint32 disable_double_yakuman = 62;
    uint32 disable_composite_yakuman = 63;
    uint32 enable_shiti = 64;
    uint32 enable_nontsumo_liqi = 65;
    uint32 disable_double_wind_four_fu = 66;
    uint32 disable_angang_guoshi = 67;
    uint32 enable_renhe = 68;
    uint32 enable_baopai_extend_settings = 69;
    uint32 fanfu = 70;
}

message ContestDetailRuleV2 {

    ContestDetailRule game_rule = 1;
    ExtraRule extra_rule = 2;

    message ExtraRule {

        uint32 required_level = 1;
        uint32 max_game_count = 2;
    }
}

message GameRuleSetting {

    uint32 round_type = 1;
    bool shiduan = 2;
    uint32 dora_count = 3;
    uint32 thinking_type = 4;
    bool use_detail_rule = 5;
    ContestDetailRuleV2 detail_rule_v2 = 6;
}

message RecordTingPaiInfo {

    string tile = 1;
    bool haveyi = 2;
    bool yiman = 3;
    uint32 count = 4;
    uint32 fu = 5;
    uint32 biao_dora_count = 6;
    bool yiman_zimo = 7;
    uint32 count_zimo = 8;
    uint32 fu_zimo = 9;
}

message RecordNoTilePlayerInfo {

    bool tingpai = 3;
    repeated string hand = 4;
    repeated RecordTingPaiInfo tings = 5;
    bool liuman = 6;
}

message RecordHuleInfo {

    repeated string hand = 1;
    repeated string ming = 2;
    string hu_tile = 3;
    uint32 seat = 4;
    bool zimo = 5;
    bool qinjia = 6;
    bool liqi = 7;
    repeated string doras = 8;
    repeated string li_doras = 9;
    bool yiman = 10;
    uint32 count = 11;
    repeated RecordFanInfo fans = 12;
    uint32 fu = 13;
    uint32 point_zimo_qin = 14;
    uint32 point_zimo_xian = 15;
    uint32 title_id = 16;
    uint32 point_sum = 17;
    uint32 dadian = 18;
    bool is_jue_zhang = 19;
    uint32 xun = 20;
    uint32 ting_type = 21;
    uint32 ting_mian = 22;

    message RecordFanInfo {

        uint32 val = 1;
        uint32 id = 2;
    }
}

message RecordHulesInfo {

    int32 seat = 1;
    repeated RecordHuleInfo hules = 2;
}

message RecordLiujuInfo {

    uint32 seat = 1;
    uint32 type = 2;
}

message RecordNoTileInfo {

    bool liujumanguan = 1;
    repeated RecordNoTilePlayerInfo players = 2;
}

message RecordLiqiInfo {

    uint32 seat = 1;
    uint32 score = 2;
    bool is_w = 3;
    bool is_zhen_ting = 4;
    uint32 xun = 5;
    bool is_success = 6;
}

message RecordGangInfo {

    uint32 seat = 1;
    uint32 type = 2;
    string pai = 3;
    bool is_dora = 4;
    uint32 xun = 5;
}

message RecordBaBeiInfo {

    uint32 seat = 1;
    bool is_zi_mo = 2;
    bool is_chong = 3;
    bool is_bei = 4;
}

message RecordPeiPaiInfo {

    uint32 dora_count = 1;
    uint32 r_dora_count = 2;
    uint32 bei_count = 3;
}

message RecordRoundInfo {

    string name = 1;
    uint32 chang = 2;
    uint32 ju = 3;
    uint32 ben = 4;
    repeated uint32 scores = 5;
    repeated RecordLiqiInfo liqi_infos = 7;
    repeated RecordGangInfo gang_infos = 8;
    repeated RecordPeiPaiInfo peipai_infos = 9;
    repeated RecordBaBeiInfo babai_infos = 10;
    RecordHulesInfo hules_info = 11;
    RecordLiujuInfo liuju_info = 12;
    RecordNoTileInfo no_tile_info = 13;
}

message RecordAnalysisedData {

    repeated RecordRoundInfo round_infos = 1;
}

message VoteData {

    uint32 activity_id = 1;
    uint32 vote = 2;
    uint32 count = 3;
}

message ActivityBuffData {

    uint32 buff_id = 1;
    uint32 level = 5;
    uint32 count = 6;
    uint32 update_time = 7;
}

message AccountResourceSnapshot {

    repeated BagItemSnapshot bag_item = 1;
    repeated CurrencySnapshot currency = 2;
    TitleSnapshot title = 3;
    UsedTitleSnapshot used_title = 4;
    uint32 currency_convert = 5;

    message BagItemSnapshot {

        uint32 resource_id = 1;
        uint32 resource_count = 2;
        uint32 resource_version = 3;
    }

    message CurrencySnapshot {

        uint32 currency_id = 1;
        uint32 currency_count = 2;
    }

    message TitleSnapshot {

        repeated uint32 title_list = 1;
    }

    message UsedTitleSnapshot {

        uint32 title_id = 1;
    }
}

message AccountCharacterSnapshot {

    repeated uint32 created_characters = 1;
    repeated Character removed_characters = 2;
    repeated Character modified_characters = 3;
    MainCharacterSnapshot main_character = 4;
    SkinsSnapshot skins = 5;
    HiddenCharacter hidden_characters = 6;

    message MainCharacterSnapshot {

        uint32 character_id = 1;
    }

    message SkinsSnapshot {

        repeated uint32 skin_list = 1;
    }

    message HiddenCharacter {

        repeated uint32 hidden_list = 1;
    }
}

message AccountMailRecord {

    repeated uint32 created_mails = 1;
    repeated MailSnapshot removed_mails = 2;
    repeated MailSnapshot modified_mails = 3;

    message MailSnapshot {

        uint32 mail_id = 1;
        uint32 reference_id = 2;
        uint32 create_time = 3;
        uint32 expire_time = 4;
        uint32 take_attachment = 5;
        repeated RewardSlot attachments = 6;
    }
}

message AccountAchievementSnapshot {

    repeated AchievementProgress achievements = 1;
    RewardedGroupSnapshot rewarded_group = 2;
    AchievementVersion version = 3;

    message RewardedGroupSnapshot {

        uint32 rewarded_id = 1;
    }

    message AchievementVersion {

        uint32 version = 1;
    }
}

message AccountMiscSnapshot {

    FaithData faith_data = 1;
    AccountVIPRewardSnapshot vip_reward_gained = 2;
    AccountVIP vip = 3;
    ShopInfo shop_info = 4;
    AccountMonthTicketSnapshot month_ticket = 5;
    AccountRechargeInfo recharged = 6;
    AccountMonthTicketSnapshotV2 month_ticket_v2 = 7;

    message AccountVIPRewardSnapshot {

        repeated uint32 rewarded = 1;
    }

    message MonthTicketInfo {

        uint32 id = 1;
        uint32 end_time = 2;
        uint32 last_pay_time = 3;
        uint32 record_start_time = 4;
        repeated uint32 history = 5;
    }

    message AccountMonthTicketSnapshot {

        repeated MonthTicketInfo tickets = 1;
    }

    message AccountVIP {

        uint32 vip = 1;
    }

    message AccountRechargeInfo {

        repeated RechargeRecord records = 1;
        uint32 has_data = 2;

        message RechargeRecord {

            uint32 level = 1;
            uint32 recharge_time = 2;
        }
    }

    message AccountMonthTicketSnapshotV2 {

        uint32 end_time = 1;
        uint32 last_pay_time = 2;
        uint32 record_start_time = 3;
        repeated uint32 history = 4;
    }
}

message AccountGiftCodeRecord {

    repeated string used_gift_code = 1;
}

message AccSn {

    AccountResourceSnapshot resource = 1;
    AccountCharacterSnapshot character = 2;
    AccountMailRecord mail = 3;
    AccountAchievementSnapshot achievement = 4;
    AccountMiscSnapshot misc = 5;
    AccountGiftCodeRecord gift_code = 6;
}

message AccSnDa {

    uint32 account_id = 1;
    uint32 time = 2;
    bytes snapshot = 3;
}

message TransparentData {

    string method = 1;
    bytes data = 2;
    string session = 3;
    NetworkEndpoint remote = 4;
}

message AmuletTile {

    uint32 id = 1;
    string tile = 2;
}

message AmuletFan {

    uint32 id = 1;
    int64 val = 2;
    uint32 count = 3;
    bool yiman = 4;
}

message AmuletReplace {

    uint32 id = 1;
    string tile = 2;
}

message AmuletMingInfo {

    uint32 type = 1;
    repeated uint32 tile_list = 2;
}

message AmuletActivityHookEffect {

    repeated uint32 add_dora = 1;
    repeated string add_tian_dora = 3;
    repeated AmuletActivityAddHookEffect add_effect = 4;
    repeated uint32 remove_effect = 5;
    repeated uint32 add_buff = 6;
    repeated uint32 remove_buff = 7;
    int32 add_coin = 9;
    repeated AmuletReplace tile_replace = 11;
    string add_fan = 12;
    string add_base = 13;
    repeated AmuletFan modify_fan = 14;
    uint32 id = 15;
    bool modify_dora = 16;
    uint32 uid = 17;
    repeated uint32 add_show_tile = 18;
    int32 add_dora_count = 19;
    repeated uint32 add_dora_no_hook = 20;
    int32 add_coin_no_hook = 21;
    repeated AmuletGameTileScoreData add_tile_score = 22;
    repeated AmuletGameTileScoreData add_tile_score_no_hook = 23;

    message AmuletActivityAddHookEffect {

        uint32 id = 1;
        uint32 uid = 2;
    }
}

message AmuletHuleInfo {

    uint32 tile = 1;
    repeated AmuletFan fan_list = 2;
    string fan = 3;
    string point = 4;
    string base = 5;
}

message AmuletHuleOperateResult {

    AmuletHuleInfo hu_final = 2;
    AmuletHuleInfo hu_base = 3;
    repeated AmuletActivityHookEffect hook_effect = 5;
}

message AmuletGangOperateResult {

    repeated uint32 new_dora = 4;
    repeated AmuletActivityHookEffect hook_effect = 5;
}

message AmuletDealTileResult {

    uint32 tile = 1;
    repeated AmuletActivityHookEffect hook_effect = 5;
}

message AmuletDiscardTileResult {

    uint32 tile = 1;
    repeated AmuletActivityHookEffect hook_effect = 5;
}

message AmuletStartGameResult {

    repeated AmuletActivityHookEffect hook_effect = 5;
}

message AmuletRoundResult {

    AmuletHuleOperateResult hu_result = 2;
    AmuletDealTileResult deal_result = 4;
    AmuletDiscardTileResult discard_result = 5;
}

message AmuletUpgradeResult {

    repeated AmuletRoundResult remain_rounds = 1;
    uint32 point_coin = 2;
    uint32 level_coin = 3;
    AmuletGameShopData shop = 4;
    repeated AmuletActivityHookEffect hook_effect = 5;
    repeated uint32 reward_pack = 6;
}

message QuestionnaireReward {

    uint32 resource_id = 1;
    uint32 count = 2;
}

message QuestionnaireDetail {

    uint32 id = 1;
    uint32 version_id = 2;
    uint32 effective_time_start = 3;
    uint32 effective_time_end = 4;
    repeated QuestionnaireReward rewards = 5;
    string banner_title = 6;
    string title = 7;
    string announcement_title = 8;
    string announcement_content = 9;
    string final_text = 10;
    repeated QuestionnaireQuestion questions = 11;
    uint32 type = 12;
}

message QuestionnaireQuestion {

    uint32 id = 1;
    string title = 2;
    string describe = 3;
    string type = 4;
    string sub_type = 5;
    repeated QuestionOption options = 6;
    bool option_random_sort = 7;
    bool require = 8;
    uint32 max_choice = 9;
    repeated NextQuestionData next_question = 10;
    repeated string matrix_row = 11;
    int32 option_random_sort_index = 12;

    message QuestionOption {

        string label = 1;
        string value = 2;
        bool allow_input = 3;
    }

    message NextQuestionData {

        uint32 target_question_id = 1;
        repeated QuestionconditionWrapper conditions = 10;

        message QuestionCondition {

            uint32 question_id = 1;
            string op = 2;
            repeated string values = 3;
        }

        message QuestionconditionWrapper {

            repeated QuestionCondition conditions = 1;
        }
    }
}

message QuestionnaireBrief {

    uint32 id = 1;
    uint32 version_id = 2;
    uint32 effective_time_start = 3;
    uint32 effective_time_end = 4;
    repeated QuestionnaireReward rewards = 5;
    string banner_title = 6;
    string title = 7;
    uint32 type = 8;
}

message SeerReport {

    string uuid = 1;
    repeated SeerEvent events = 2;
    repeated SeerRound rounds = 3;
}

message SeerEvent {

    int32 record_index = 1;
    int32 seer_index = 2;
    repeated SeerRecommend recommends = 3;
}

message SeerRecommend {

    int32 seat = 1;
    repeated SeerPrediction predictions = 2;
}

message SeerPrediction {

    int32 action = 1;
    int32 score = 2;
}

message SeerRound {

    uint32 chang = 1;
    uint32 ju = 2;
    uint32 ben = 3;
    repeated SeerScore player_scores = 4;
}

message SeerScore {

    uint32 seat = 1;
    uint32 rating = 2;
}

message SeerBrief {

    string uuid = 1;
    uint32 state = 2;
    uint32 expire_time = 3;
    repeated SeerScore player_scores = 4;
    uint32 create_time = 5;
}

message SimulationV2SeasonData {

    uint32 round = 1;
    SimulationV2Ability ability = 2;
    repeated SimulationV2Effect effect_list = 3;
    SimulationV2Match match = 4;
    SimulationV2Event event = 5;
    repeated SimulationV2EventHistory event_history = 6;
    SimulationV2Record record = 7;
    int32 total_score = 8;
    repeated SimulationV2MatchRecord match_history = 9;
}

message SimulationV2PlayerRecord {

    uint32 id = 1;
    bool main = 2;
    int32 score = 3;
    uint32 rank = 4;
    uint32 seat = 5;
}

message SimulationV2MatchRecord {

    repeated SimulationV2PlayerRecord players = 1;
    uint32 round = 2;
}

message SimulationV2EventHistory {

    uint32 id = 1;
    uint32 round = 2;
}

message SimulationV2Event {

    uint32 id = 1;
    repeated SimulationV2EventSelection selections = 2;
    uint32 next_round = 3;

    message SimulationV2EventSelection {

        uint32 id = 1;
        repeated SimulationV2EventResult results = 2;

        message SimulationV2EventResult {

            uint32 id = 1;
            uint32 weight = 2;
        }
    }
}

message SimulationV2Ability {

    uint32 luk = 1;
    uint32 tec = 2;
    uint32 ins = 3;
    uint32 int = 4;
    uint32 res = 5;
}

message SimulationV2Buff {

    uint32 id = 1;
    uint32 round = 2;
    repeated uint32 store = 3;
}

message SimulationV2Effect {

    uint32 id = 1;
}

message SimulationV2MatchInfo {

    uint32 chang = 1;
    uint32 ju = 2;
    uint32 ben = 3;
    uint32 gong = 4;
    uint32 remain = 5;
}

message SimulationV2Record {

    uint32 hu_count = 1;
    uint32 chong_count = 2;
    uint32 highest_hu = 3;
    repeated uint32 rank = 4;
    uint32 round_count = 5;
}

message SimulationV2MatchHistory {

    uint32 type = 1;
    uint32 remain = 2;
    repeated int32 score_modify = 3;
    RoundStartArgs round_start = 4;
    RiichiArgs riichi = 5;
    FuluArgs fulu = 6;
    repeated HuleArgs hule = 7;
    PushTingArgs push_ting = 8;
    FindTingArgs find_ting = 9;
    LiujuArgs liuju = 10;
    StoryArgs story = 11;

    message RoundStartArgs {

        SimulationV2MatchInfo info = 1;
        repeated int32 scores = 2;
        uint32 ting = 3;
        repeated uint32 effected_buff_list = 4;
    }

    message RiichiArgs {

        uint32 seat = 1;
    }

    message FuluArgs {

        uint32 seat = 1;
        uint32 ting = 2;
        uint32 fulu = 3;
    }

    message HuleArgs {

        uint32 seat = 1;
        bool zimo = 2;
        uint32 chong_seat = 3;
        int32 point = 4;
        int32 fan = 5;
        repeated int32 score_modify = 6;
    }

    message PushTingArgs {

        uint32 seat = 1;
        uint32 ting = 2;
    }

    message FindTingArgs {

        uint32 seat = 1;
        uint32 target = 2;
    }

    message LiujuArgs {

        repeated uint32 ting = 1;
    }

    message StoryArgs {

        uint32 story_id = 1;
    }
}

message SimulationV2Match {

    SimulationV2MatchInfo info = 1;
    repeated SimulationV2Player players = 2;
    repeated SimulationV2MatchHistory history = 3;
    repeated uint32 rank = 4;
    bool is_match_end = 5;
    repeated SimulationActionData actions = 6;
    repeated SimulationV2Buff buff_list = 9;
    bool is_first_round = 10;
    uint32 last_event_remain = 11;
    repeated uint32 effected_buff_list = 12;
    repeated uint32 triggered_story = 13;

    message SimulationV2Player {

        uint32 id = 1;
        bool main = 2;
        uint32 ting = 4;
        int32 score = 5;
        uint32 fulu = 6;
        bool riichi = 7;
        repeated uint32 find_ting = 8;
        uint32 seat = 9;
        uint32 con_push_ting = 10;
        uint32 con_keep_ting = 11;
        bool ippatsu = 12;
    }
}

message SimulationActionData {

    uint32 type = 1;
    ActionRiichiData riichi = 2;
    ActionHuleData hule = 3;
    ActionFuluData fulu = 4;
    ActionDiscardData discard_tile = 5;
    ActionDealTileData deal_tile = 6;

    message ActionRiichiData {

        uint32 seat = 1;
    }

    message ActionHuleData {

        repeated HuleInfo hule = 1;

        message HuleInfo {

            uint32 fan = 1;
            bool zimo = 2;
            uint32 point = 3;
            bool oya = 4;
            uint32 player = 5;
            uint32 chong = 6;
            bool toutiao = 7;
        }
    }

    message ActionFuluData {

        uint32 seat = 1;
    }

    message ActionDiscardData {

        uint32 seat = 1;
        bool riichi = 2;
    }

    message ActionDealTileData {

        uint32 seat = 1;
    }
}
service Lobby {
    rpc fetchConnectionInfo (ReqCommon) returns (ResConnectionInfo);
    rpc fetchQueueInfo (ReqCommon) returns (ResFetchQueueInfo);
    rpc cancelQueue (ReqCommon) returns (ResCommon);
    rpc openidCheck (ReqOpenidCheck) returns (ResOauth2Check);
    rpc signup (ReqSignupAccount) returns (ResSignupAccount);
    rpc login (ReqLogin) returns (ResLogin);
    rpc prepareLogin (ReqPrepareLogin) returns (ResCommon);
    rpc fastLogin (ReqCommon) returns (ResFastLogin);
    rpc fetchInfo (ReqCommon) returns (ResFetchInfo);
    rpc loginSuccess (ReqCommon) returns (ResCommon);
    rpc fetchServerMaintenanceInfo (ReqCommon) returns (ResFetchServerMaintenanceInfo);
    rpc emailLogin (ReqEmailLogin) returns (ResLogin);
    rpc oauth2Auth (ReqOauth2Auth) returns (ResOauth2Auth);
    rpc oauth2Check (ReqOauth2Check) returns (ResOauth2Check);
    rpc oauth2Signup (ReqOauth2Signup) returns (ResOauth2Signup);
    rpc oauth2Login (ReqOauth2Login) returns (ResLogin);
    rpc dmmPreLogin (ReqDMMPreLogin) returns (ResDMMPreLogin);
    rpc createPhoneVerifyCode (ReqCreatePhoneVerifyCode) returns (ResCommon);
    rpc createEmailVerifyCode (ReqCreateEmailVerifyCode) returns (ResCommon);
    rpc verfifyCodeForSecure (ReqVerifyCodeForSecure) returns (ResVerfiyCodeForSecure);
    rpc bindPhoneNumber (ReqBindPhoneNumber) returns (ResCommon);
    rpc unbindPhoneNumber (ReqUnbindPhoneNumber) returns (ResCommon);
    rpc fetchPhoneLoginBind (ReqCommon) returns (ResFetchPhoneLoginBind);
    rpc createPhoneLoginBind (ReqCreatePhoneLoginBind) returns (ResCommon);
    rpc bindEmail (ReqBindEmail) returns (ResCommon);
    rpc modifyPassword (ReqModifyPassword) returns (ResCommon);
    rpc bindAccount (ReqBindAccount) returns (ResCommon);
    rpc logout (ReqLogout) returns (ResLogout);
    rpc heatbeat (ReqHeatBeat) returns (ResCommon);
    rpc searchAccountByEid (ReqSearchAccountByEidLobby) returns (ResSearchAccountbyEidLobby);
    rpc loginBeat (ReqLoginBeat) returns (ResCommon);
    rpc createNickname (ReqCreateNickname) returns (ResCommon);
    rpc modifyNickname (ReqModifyNickname) returns (ResCommon);
    rpc modifyBirthday (ReqModifyBirthday) returns (ResCommon);
    rpc fetchRoom (ReqCommon) returns (ResSelfRoom);
    rpc fetchGamingInfo (ReqCommon) returns (ResFetchGamingInfo);
    rpc createRoom (ReqCreateRoom) returns (ResCreateRoom);
    rpc joinRoom (ReqJoinRoom) returns (ResJoinRoom);
    rpc leaveRoom (ReqCommon) returns (ResCommon);
    rpc readyPlay (ReqRoomReady) returns (ResCommon);
    rpc dressingStatus (ReqRoomDressing) returns (ResCommon);
    rpc startRoom (ReqRoomStart) returns (ResCommon);
    rpc roomKickPlayer (ReqRoomKickPlayer) returns (ResCommon);
    rpc modifyRoom (ReqModifyRoom) returns (ResCommon);
    rpc addRoomRobot (ReqAddRoomRobot) returns (ResCommon);
    rpc matchGame (ReqJoinMatchQueue) returns (ResCommon);
    rpc cancelMatch (ReqCancelMatchQueue) returns (ResCommon);
    rpc fetchAccountInfo (ReqAccountInfo) returns (ResAccountInfo);
    rpc changeAvatar (ReqChangeAvatar) returns (ResCommon);
    rpc receiveVersionReward (ReqCommon) returns (ResCommon);
    rpc fetchAccountStatisticInfo (ReqAccountStatisticInfo) returns (ResAccountStatisticInfo);
    rpc fetchAccountChallengeRankInfo (ReqAccountInfo) returns (ResAccountChallengeRankInfo);
    rpc fetchAccountCharacterInfo (ReqCommon) returns (ResAccountCharacterInfo);
    rpc shopPurchase (ReqShopPurchase) returns (ResShopPurchase);
    rpc fetchGameRecord (ReqGameRecord) returns (ResGameRecord);
    rpc readGameRecord (ReqGameRecord) returns (ResCommon);
    rpc fetchGameRecordList (ReqGameRecordList) returns (ResGameRecordList);
    rpc fetchGameRecordListV2 (ReqGameRecordListV2) returns (ResGameRecordListV2);
    rpc fetchNextGameRecordList (ReqNextGameRecordList) returns (ResNextGameRecordList);
    rpc fetchCollectedGameRecordList (ReqCommon) returns (ResCollectedGameRecordList);
    rpc fetchGameRecordsDetail (ReqGameRecordsDetail) returns (ResGameRecordsDetail);
    rpc fetchGameRecordsDetailV2 (ReqGameRecordsDetailV2) returns (ResGameRecordsDetailV2);
    rpc addCollectedGameRecord (ReqAddCollectedGameRecord) returns (ResAddCollectedGameRecord);
    rpc removeCollectedGameRecord (ReqRemoveCollectedGameRecord) returns (ResRemoveCollectedGameRecord);
    rpc changeCollectedGameRecordRemarks (ReqChangeCollectedGameRecordRemarks) returns (ResChangeCollectedGameRecordRemarks);
    rpc fetchLevelLeaderboard (ReqLevelLeaderboard) returns (ResLevelLeaderboard);
    rpc fetchChallengeLeaderboard (ReqChallangeLeaderboard) returns (ResChallengeLeaderboard);
    rpc fetchMutiChallengeLevel (ReqMutiChallengeLevel) returns (ResMutiChallengeLevel);
    rpc fetchMultiAccountBrief (ReqMultiAccountId) returns (ResMultiAccountBrief);
    rpc fetchFriendList (ReqCommon) returns (ResFriendList);
    rpc fetchFriendApplyList (ReqCommon) returns (ResFriendApplyList);
    rpc applyFriend (ReqApplyFriend) returns (ResCommon);
    rpc handleFriendApply (ReqHandleFriendApply) returns (ResCommon);
    rpc removeFriend (ReqRemoveFriend) returns (ResCommon);
    rpc searchAccountById (ReqSearchAccountById) returns (ResSearchAccountById);
    rpc searchAccountByPattern (ReqSearchAccountByPattern) returns (ResSearchAccountByPattern);
    rpc fetchAccountState (ReqAccountList) returns (ResAccountStates);
    rpc fetchBagInfo (ReqCommon) returns (ResBagInfo);
    rpc useBagItem (ReqUseBagItem) returns (ResCommon);
    rpc openManualItem (ReqOpenManualItem) returns (ResCommon);
    rpc openRandomRewardItem (ReqOpenRandomRewardItem) returns (ResOpenRandomRewardItem);
    rpc openAllRewardItem (ReqOpenAllRewardItem) returns (ResOpenAllRewardItem);
    rpc composeShard (ReqComposeShard) returns (ResCommon);
    rpc fetchAnnouncement (ReqFetchAnnouncement) returns (ResAnnouncement);
    rpc readAnnouncement (ReqReadAnnouncement) returns (ResCommon);
    rpc fetchMailInfo (ReqCommon) returns (ResMailInfo);
    rpc readMail (ReqReadMail) returns (ResCommon);
    rpc deleteMail (ReqDeleteMail) returns (ResCommon);
    rpc takeAttachmentFromMail (ReqTakeAttachment) returns (ResCommon);
    rpc receiveAchievementReward (ReqReceiveAchievementReward) returns (ResReceiveAchievementReward);
    rpc receiveAchievementGroupReward (ReqReceiveAchievementGroupReward) returns (ResReceiveAchievementGroupReward);
    rpc fetchAchievementRate (ReqCommon) returns (ResFetchAchievementRate);
    rpc fetchAchievement (ReqCommon) returns (ResAchievement);
    rpc buyShiLian (ReqBuyShiLian) returns (ResCommon);
    rpc matchShiLian (ReqCommon) returns (ResCommon);
    rpc goNextShiLian (ReqCommon) returns (ResCommon);
    rpc updateClientValue (ReqUpdateClientValue) returns (ResCommon);
    rpc fetchClientValue (ReqCommon) returns (ResClientValue);
    rpc clientMessage (ReqClientMessage) returns (ResCommon);
    rpc fetchCurrentMatchInfo (ReqCurrentMatchInfo) returns (ResCurrentMatchInfo);
    rpc userComplain (ReqUserComplain) returns (ResCommon);
    rpc fetchReviveCoinInfo (ReqCommon) returns (ResReviveCoinInfo);
    rpc gainReviveCoin (ReqCommon) returns (ResCommon);
    rpc fetchDailyTask (ReqCommon) returns (ResDailyTask);
    rpc refreshDailyTask (ReqRefreshDailyTask) returns (ResRefreshDailyTask);
    rpc useGiftCode (ReqUseGiftCode) returns (ResUseGiftCode);
    rpc useSpecialGiftCode (ReqUseGiftCode) returns (ResUseSpecialGiftCode);
    rpc fetchTitleList (ReqCommon) returns (ResTitleList);
    rpc useTitle (ReqUseTitle) returns (ResCommon);
    rpc sendClientMessage (ReqSendClientMessage) returns (ResCommon);
    rpc fetchGameLiveInfo (ReqGameLiveInfo) returns (ResGameLiveInfo);
    rpc fetchGameLiveLeftSegment (ReqGameLiveLeftSegment) returns (ResGameLiveLeftSegment);
    rpc fetchGameLiveList (ReqGameLiveList) returns (ResGameLiveList);
    rpc fetchCommentSetting (ReqCommon) returns (ResCommentSetting);
    rpc updateCommentSetting (ReqUpdateCommentSetting) returns (ResCommon);
    rpc fetchCommentList (ReqFetchCommentList) returns (ResFetchCommentList);
    rpc fetchCommentContent (ReqFetchCommentContent) returns (ResFetchCommentContent);
    rpc leaveComment (ReqLeaveComment) returns (ResCommon);
    rpc deleteComment (ReqDeleteComment) returns (ResCommon);
    rpc updateReadComment (ReqUpdateReadComment) returns (ResCommon);
    rpc fetchRollingNotice (ReqFetchRollingNotice) returns (ResFetchRollingNotice);
    rpc fetchMaintainNotice (ReqCommon) returns (ResFetchMaintainNotice);
    rpc fetchServerTime (ReqCommon) returns (ResServerTime);
    rpc fetchPlatformProducts (ReqPlatformBillingProducts) returns (ResPlatformBillingProducts);
    rpc fetchRandomCharacter (ReqCommon) returns (ResRandomCharacter);
    rpc setRandomCharacter (ReqRandomCharacter) returns (ResCommon);
    rpc cancelGooglePlayOrder (ReqCancelGooglePlayOrder) returns (ResCommon);
    rpc openChest (ReqOpenChest) returns (ResOpenChest);
    rpc buyFromChestShop (ReqBuyFromChestShop) returns (ResBuyFromChestShop);
    rpc fetchDailySignInInfo (ReqCommon) returns (ResDailySignInInfo);
    rpc doDailySignIn (ReqCommon) returns (ResCommon);
    rpc doActivitySignIn (ReqDoActivitySignIn) returns (ResDoActivitySignIn);
    rpc fetchCharacterInfo (ReqCommon) returns (ResCharacterInfo);
    rpc updateCharacterSort (ReqUpdateCharacterSort) returns (ResCommon);
    rpc changeMainCharacter (ReqChangeMainCharacter) returns (ResCommon);
    rpc changeCharacterSkin (ReqChangeCharacterSkin) returns (ResCommon);
    rpc changeCharacterView (ReqChangeCharacterView) returns (ResCommon);
    rpc setHiddenCharacter (ReqSetHiddenCharacter) returns (ResSetHiddenCharacter);
    rpc sendGiftToCharacter (ReqSendGiftToCharacter) returns (ResSendGiftToCharacter);
    rpc sellItem (ReqSellItem) returns (ResCommon);
    rpc fetchCommonView (ReqCommon) returns (ResCommonView);
    rpc changeCommonView (ReqChangeCommonView) returns (ResCommon);
    rpc saveCommonViews (ReqSaveCommonViews) returns (ResCommon);
    rpc fetchCommonViews (ReqCommonViews) returns (ResCommonViews);
    rpc fetchAllCommonViews (ReqCommon) returns (ResAllcommonViews);
    rpc useCommonView (ReqUseCommonView) returns (ResCommon);
    rpc upgradeCharacter (ReqUpgradeCharacter) returns (ResUpgradeCharacter);
    rpc addFinishedEnding (ReqFinishedEnding) returns (ResCommon);
    rpc receiveEndingReward (ReqFinishedEnding) returns (ResCommon);
    rpc gameMasterCommand (ReqGMCommand) returns (ResCommon);
    rpc fetchShopInfo (ReqCommon) returns (ResShopInfo);
    rpc buyFromShop (ReqBuyFromShop) returns (ResBuyFromShop);
    rpc buyFromZHP (ReqBuyFromZHP) returns (ResCommon);
    rpc refreshZHPShop (ReqReshZHPShop) returns (ResRefreshZHPShop);
    rpc fetchMonthTicketInfo (ReqCommon) returns (ResMonthTicketInfo);
    rpc payMonthTicket (ReqCommon) returns (ResPayMonthTicket);
    rpc exchangeCurrency (ReqExchangeCurrency) returns (ResCommon);
    rpc exchangeChestStone (ReqExchangeCurrency) returns (ResCommon);
    rpc exchangeDiamond (ReqExchangeCurrency) returns (ResCommon);
    rpc fetchServerSettings (ReqCommon) returns (ResServerSettings);
    rpc fetchAccountSettings (ReqCommon) returns (ResAccountSettings);
    rpc updateAccountSettings (ReqUpdateAccountSettings) returns (ResCommon);
    rpc fetchModNicknameTime (ReqCommon) returns (ResModNicknameTime);
    rpc createWechatNativeOrder (ReqCreateWechatNativeOrder) returns (ResCreateWechatNativeOrder);
    rpc createWechatAppOrder (ReqCreateWechatAppOrder) returns (ResCreateWechatAppOrder);
    rpc createAlipayOrder (ReqCreateAlipayOrder) returns (ResCreateAlipayOrder);
    rpc createAlipayScanOrder (ReqCreateAlipayScanOrder) returns (ResCreateAlipayScanOrder);
    rpc createAlipayAppOrder (ReqCreateAlipayAppOrder) returns (ResCreateAlipayAppOrder);
    rpc createJPCreditCardOrder (ReqCreateJPCreditCardOrder) returns (ResCreateJPCreditCardOrder);
    rpc createJPPaypalOrder (ReqCreateJPPaypalOrder) returns (ResCreateJPPaypalOrder);
    rpc createJPAuOrder (ReqCreateJPAuOrder) returns (ResCreateJPAuOrder);
    rpc createJPDocomoOrder (ReqCreateJPDocomoOrder) returns (ResCreateJPDocomoOrder);
    rpc createJPWebMoneyOrder (ReqCreateJPWebMoneyOrder) returns (ResCreateJPWebMoneyOrder);
    rpc createJPSoftbankOrder (ReqCreateJPSoftbankOrder) returns (ResCreateJPSoftbankOrder);
    rpc createJPPayPayOrder (ReqCreateJPPayPayOrder) returns (ResCreateJPPayPayOrder);
    rpc fetchJPCommonCreditCardOrder (ReqFetchJPCommonCreditCardOrder) returns (ResFetchJPCommonCreditCardOrder);
    rpc createJPGMOOrder (ReqCreateJPGMOOrder) returns (ResCreateJPGMOOrder);
    rpc createENPaypalOrder (ReqCreateENPaypalOrder) returns (ResCreateENPaypalOrder);
    rpc createENMasterCardOrder (ReqCreateENMasterCardOrder) returns (ResCreateENMasterCardOrder);
    rpc createENVisaOrder (ReqCreateENVisaOrder) returns (ResCreateENVisaOrder);
    rpc createENJCBOrder (ReqCreateENJCBOrder) returns (ResCreateENJCBOrder);
    rpc createENAlipayOrder (ReqCreateENAlipayOrder) returns (ResCreateENAlipayOrder);
    rpc createKRPaypalOrder (ReqCreateKRPaypalOrder) returns (ResCreateKRPaypalOrder);
    rpc createKRMasterCardOrder (ReqCreateKRMasterCardOrder) returns (ResCreateKRMasterCardOrder);
    rpc createKRVisaOrder (ReqCreateKRVisaOrder) returns (ResCreateKRVisaOrder);
    rpc createKRJCBOrder (ReqCreateKRJCBOrder) returns (ResCreateKRJCBOrder);
    rpc createKRAlipayOrder (ReqCreateKRAlipayOrder) returns (ResCreateKRAlipayOrder);
    rpc createDMMOrder (ReqCreateDMMOrder) returns (ResCreateDmmOrder);
    rpc createIAPOrder (ReqCreateIAPOrder) returns (ResCreateIAPOrder);
    rpc createSteamOrder (ReqCreateSteamOrder) returns (ResCreateSteamOrder);
    rpc verifySteamOrder (ReqVerifySteamOrder) returns (ResCommon);
    rpc createMyCardAndroidOrder (ReqCreateMyCardOrder) returns (ResCreateMyCardOrder);
    rpc createMyCardWebOrder (ReqCreateMyCardOrder) returns (ResCreateMyCardOrder);
    rpc createPaypalOrder (ReqCreatePaypalOrder) returns (ResCreatePaypalOrder);
    rpc createXsollaOrder (ReqCreateXsollaOrder) returns (ResCreateXsollaOrder);
    rpc createXsollaV4Order (ReqCreateXsollaOrder) returns (ResCreateXsollaOrder);
    rpc verifyMyCardOrder (ReqVerifyMyCardOrder) returns (ResCommon);
    rpc verificationIAPOrder (ReqVerificationIAPOrder) returns (ResVerificationIAPOrder);
    rpc createYostarSDKOrder (ReqCreateYostarOrder) returns (ResCreateYostarOrder);
    rpc createBillingOrder (ReqCreateBillingOrder) returns (ResCreateBillingOrder);
    rpc solveGooglePlayOrder (ReqSolveGooglePlayOrder) returns (ResCommon);
    rpc solveGooglePayOrderV3 (ReqSolveGooglePlayOrderV3) returns (ResCommon);
    rpc deliverAA32Order (ReqDeliverAA32Order) returns (ResCommon);
    rpc fetchMisc (ReqCommon) returns (ResMisc);
    rpc modifySignature (ReqModifySignature) returns (ResCommon);
    rpc fetchIDCardInfo (ReqCommon) returns (ResIDCardInfo);
    rpc updateIDCardInfo (ReqUpdateIDCardInfo) returns (ResCommon);
    rpc fetchVipReward (ReqCommon) returns (ResVipReward);
    rpc gainVipReward (ReqGainVipReward) returns (ResCommon);
    rpc fetchRefundOrder (ReqCommon) returns (ResFetchRefundOrder);
    rpc fetchCustomizedContestList (ReqFetchCustomizedContestList) returns (ResFetchCustomizedContestList);
    rpc fetchCustomizedContestAuthInfo (ReqFetchCustomizedContestAuthInfo) returns (ResFetchCustomizedContestAuthInfo);
    rpc enterCustomizedContest (ReqEnterCustomizedContest) returns (ResEnterCustomizedContest);
    rpc leaveCustomizedContest (ReqCommon) returns (ResCommon);
    rpc fetchCustomizedContestOnlineInfo (ReqFetchCustomizedContestOnlineInfo) returns (ResFetchCustomizedContestOnlineInfo);
    rpc fetchCustomizedContestByContestId (ReqFetchCustomizedContestByContestId) returns (ResFetchCustomizedContestByContestId);
    rpc signupCustomizedContest (ReqSignupCustomizedContest) returns (ResSignupCustomizedContest);
    rpc startCustomizedContest (ReqStartCustomizedContest) returns (ResCommon);
    rpc stopCustomizedContest (ReqStopCustomizedContest) returns (ResCommon);
    rpc joinCustomizedContestChatRoom (ReqJoinCustomizedContestChatRoom) returns (ResJoinCustomizedContestChatRoom);
    rpc leaveCustomizedContestChatRoom (ReqCommon) returns (ResCommon);
    rpc sayChatMessage (ReqSayChatMessage) returns (ResCommon);
    rpc fetchCustomizedContestGameRecords (ReqFetchCustomizedContestGameRecords) returns (ResFetchCustomizedContestGameRecords);
    rpc fetchCustomizedContestGameLiveList (ReqFetchCustomizedContestGameLiveList) returns (ResFetchCustomizedContestGameLiveList);
    rpc followCustomizedContest (ReqTargetCustomizedContest) returns (ResCommon);
    rpc unfollowCustomizedContest (ReqTargetCustomizedContest) returns (ResCommon);
    rpc fetchActivityList (ReqCommon) returns (ResActivityList);
    rpc fetchAccountActivityData (ReqCommon) returns (ResAccountActivityData);
    rpc exchangeActivityItem (ReqExchangeActivityItem) returns (ResExchangeActivityItem);
    rpc completeActivityTask (ReqCompleteActivityTask) returns (ResCommon);
    rpc completeActivityTaskBatch (ReqCompleteActivityTaskBatch) returns (ResCommon);
    rpc completeActivityFlipTask (ReqCompleteActivityTask) returns (ResCommon);
    rpc completePeriodActivityTask (ReqCompleteActivityTask) returns (ResCommon);
    rpc completePeriodActivityTaskBatch (ReqCompletePeriodActivityTaskBatch) returns (ResCommon);
    rpc completeRandomActivityTask (ReqCompleteActivityTask) returns (ResCommon);
    rpc completeRandomActivityTaskBatch (ReqCompleteActivityTaskBatch) returns (ResCommon);
    rpc receiveActivityFlipTask (ReqReceiveActivityFlipTask) returns (ResReceiveActivityFlipTask);
    rpc completeSegmentTaskReward (ReqCompleteSegmentTaskReward) returns (ResCompleteSegmentTaskReward);
    rpc fetchActivityFlipInfo (ReqFetchActivityFlipInfo) returns (ResFetchActivityFlipInfo);
    rpc gainAccumulatedPointActivityReward (ReqGainAccumulatedPointActivityReward) returns (ResCommon);
    rpc gainMultiPointActivityReward (ReqGainMultiPointActivityReward) returns (ResCommon);
    rpc fetchRankPointLeaderboard (ReqFetchRankPointLeaderboard) returns (ResFetchRankPointLeaderboard);
    rpc gainRankPointReward (ReqGainRankPointReward) returns (ResCommon);
    rpc richmanActivityNextMove (ReqRichmanNextMove) returns (ResRichmanNextMove);
    rpc richmanAcitivitySpecialMove (ReqRichmanSpecialMove) returns (ResRichmanNextMove);
    rpc richmanActivityChestInfo (ReqRichmanChestInfo) returns (ResRichmanChestInfo);
    rpc createGameObserveAuth (ReqCreateGameObserveAuth) returns (ResCreateGameObserveAuth);
    rpc refreshGameObserveAuth (ReqRefreshGameObserveAuth) returns (ResRefreshGameObserveAuth);
    rpc fetchActivityBuff (ReqCommon) returns (ResActivityBuff);
    rpc upgradeActivityBuff (ReqUpgradeActivityBuff) returns (ResActivityBuff);
    rpc upgradeActivityLevel (ReqUpgradeActivityLevel) returns (ResUpgradeActivityLevel);
    rpc receiveUpgradeActivityReward (ReqReceiveUpgradeActivityReward) returns (ResReceiveUpgradeActivityReward);
    rpc upgradeChallenge (ReqCommon) returns (ResUpgradeChallenge);
    rpc refreshChallenge (ReqCommon) returns (ResRefreshChallenge);
    rpc fetchChallengeInfo (ReqCommon) returns (ResFetchChallengeInfo);
    rpc forceCompleteChallengeTask (ReqForceCompleteChallengeTask) returns (ResCommon);
    rpc fetchChallengeSeason (ReqCommon) returns (ResChallengeSeasonInfo);
    rpc receiveChallengeRankReward (ReqReceiveChallengeRankReward) returns (ResReceiveChallengeRankReward);
    rpc fetchABMatchInfo (ReqCommon) returns (ResFetchABMatch);
    rpc buyInABMatch (ReqBuyInABMatch) returns (ResCommon);
    rpc receiveABMatchReward (ReqCommon) returns (ResCommon);
    rpc quitABMatch (ReqCommon) returns (ResCommon);
    rpc startUnifiedMatch (ReqStartUnifiedMatch) returns (ResCommon);
    rpc cancelUnifiedMatch (ReqCancelUnifiedMatch) returns (ResCommon);
    rpc fetchGamePointRank (ReqGamePointRank) returns (ResGamePointRank);
    rpc fetchSelfGamePointRank (ReqGamePointRank) returns (ResFetchSelfGamePointRank);
    rpc readSNS (ReqReadSNS) returns (ResReadSNS);
    rpc replySNS (ReqReplySNS) returns (ResReplySNS);
    rpc likeSNS (ReqLikeSNS) returns (ResLikeSNS);
    rpc digMine (ReqDigMine) returns (ResDigMine);
    rpc fetchLastPrivacy (ReqFetchLastPrivacy) returns (ResFetchLastPrivacy);
    rpc checkPrivacy (ReqCheckPrivacy) returns (ResCommon);
    rpc fetchRPGBattleHistory (ReqFetchRPGBattleHistory) returns (ResFetchRPGBattleHistory);
    rpc fetchRPGBattleHistoryV2 (ReqFetchRPGBattleHistory) returns (ResFetchRPGBattleHistoryV2);
    rpc receiveRPGRewards (ReqReceiveRPGRewards) returns (ResReceiveRPGRewards);
    rpc receiveRPGReward (ReqReceiveRPGReward) returns (ResReceiveRPGRewards);
    rpc buyArenaTicket (ReqBuyArenaTicket) returns (ResCommon);
    rpc enterArena (ReqEnterArena) returns (ResCommon);
    rpc receiveArenaReward (ReqArenaReward) returns (ResArenaReward);
    rpc fetchOBToken (ReqFetchOBToken) returns (ResFetchOBToken);
    rpc receiveCharacterRewards (ReqReceiveCharacterRewards) returns (ResReceiveCharacterRewards);
    rpc feedActivityFeed (ReqFeedActivityFeed) returns (ResFeedActivityFeed);
    rpc sendActivityGiftToFriend (ReqSendActivityGiftToFriend) returns (ResSendActivityGiftToFriend);
    rpc receiveActivityGift (ReqReceiveActivityGift) returns (ResCommon);
    rpc receiveAllActivityGift (ReqReceiveAllActivityGift) returns (ResReceiveAllActivityGift);
    rpc fetchFriendGiftActivityData (ReqFetchFriendGiftActivityData) returns (ResFetchFriendGiftActivityData);
    rpc openPreChestItem (ReqOpenPreChestItem) returns (ResOpenPreChestItem);
    rpc fetchVoteActivity (ReqFetchVoteActivity) returns (ResFetchVoteActivity);
    rpc voteActivity (ReqVoteActivity) returns (ResVoteActivity);
    rpc unlockActivitySpot (ReqUnlockActivitySpot) returns (ResCommon);
    rpc unlockActivitySpotEnding (ReqUnlockActivitySpotEnding) returns (ResCommon);
    rpc receiveActivitySpotReward (ReqReceiveActivitySpotReward) returns (ResReceiveActivitySpotReward);
    rpc deleteAccount (ReqCommon) returns (ResDeleteAccount);
    rpc cancelDeleteAccount (ReqCommon) returns (ResCommon);
    rpc logReport (ReqLogReport) returns (ResCommon);
    rpc bindOauth2 (ReqBindOauth2) returns (ResCommon);
    rpc fetchOauth2Info (ReqFetchOauth2) returns (ResFetchOauth2);
    rpc setLoadingImage (ReqSetLoadingImage) returns (ResCommon);
    rpc fetchShopInterval (ReqCommon) returns (ResFetchShopInterval);
    rpc fetchActivityInterval (ReqCommon) returns (ResFetchActivityInterval);
    rpc fetchRecentFriend (ReqCommon) returns (ResFetchrecentFriend);
    rpc openGacha (ReqOpenGacha) returns (ResOpenGacha);
    rpc taskRequest (ReqTaskRequest) returns (ResCommon);
    rpc simulationActivityTrain (ReqSimulationActivityTrain) returns (ResSimulationActivityTrain);
    rpc fetchSimulationGameRecord (ReqFetchSimulationGameRecord) returns (ResFetchSimulationGameRecord);
    rpc startSimulationActivityGame (ReqStartSimulationActivityGame) returns (ResStartSimulationActivityGame);
    rpc fetchSimulationGameRank (ReqFetchSimulationGameRank) returns (ResFetchSimulationGameRank);
    rpc generateCombiningCraft (ReqGenerateCombiningCraft) returns (ResGenerateCombiningCraft);
    rpc moveCombiningCraft (ReqMoveCombiningCraft) returns (ResMoveCombiningCraft);
    rpc combiningRecycleCraft (ReqCombiningRecycleCraft) returns (ResCombiningRecycleCraft);
    rpc recoverCombiningRecycle (ReqRecoverCombiningRecycle) returns (ResRecoverCombiningRecycle);
    rpc finishCombiningOrder (ReqFinishCombiningOrder) returns (ResFinishCombiningOrder);
    rpc upgradeVillageBuilding (ReqUpgradeVillageBuilding) returns (ResCommon);
    rpc receiveVillageBuildingReward (ReqReceiveVillageBuildingReward) returns (ResReceiveVillageBuildingReward);
    rpc startVillageTrip (ReqStartVillageTrip) returns (ResCommon);
    rpc receiveVillageTripReward (ReqReceiveVillageTripReward) returns (ResReceiveVillageTripReward);
    rpc completeVillageTask (ReqCompleteVillageTask) returns (ResCompleteVillageTask);
    rpc getFriendVillageData (ReqGetFriendVillageData) returns (ResGetFriendVillageData);
    rpc setVillageWorker (ReqSetVillageWorker) returns (ResSetVillageWorker);
    rpc nextRoundVillage (ReqNextRoundVillage) returns (ResNextRoundVillage);
    rpc resolveFestivalActivityProposal (ReqResolveFestivalActivityProposal) returns (ResResolveFestivalActivityProposal);
    rpc resolveFestivalActivityEvent (ReqResolveFestivalActivityEvent) returns (ResResolveFestivalActivityEvent);
    rpc buyFestivalProposal (ReqBuyFestivalProposal) returns (ResBuyFestivalProposal);
    rpc islandActivityMove (ReqIslandActivityMove) returns (ResCommon);
    rpc islandActivityBuy (ReqIslandActivityBuy) returns (ResCommon);
    rpc islandActivitySell (ReqIslandActivitySell) returns (ResCommon);
    rpc islandActivityTidyBag (ReqIslandActivityTidyBag) returns (ResCommon);
    rpc islandActivityUnlockBagGrid (ReqIslandActivityUnlockBagGrid) returns (ResCommon);
    rpc createCustomizedContest (ReqCreateCustomizedContest) returns (ResCreateCustomizedContest);
    rpc fetchManagerCustomizedContestList (ReqFetchmanagerCustomizedContestList) returns (ResFetchManagerCustomizedContestList);
    rpc fetchManagerCustomizedContest (ReqFetchManagerCustomizedContest) returns (ResFetchManagerCustomizedContest);
    rpc updateManagerCustomizedContest (ReqUpdateManagerCustomizedContest) returns (ResCommon);
    rpc fetchContestPlayerRank (ReqFetchContestPlayerRank) returns (ResFetchContestPlayerRank);
    rpc fetchReadyPlayerList (ReqFetchReadyPlayerList) returns (ResFetchReadyPlayerList);
    rpc createGamePlan (ReqCreateGamePlan) returns (ResCommon);
    rpc generateContestManagerLoginCode (ReqCommon) returns (ResGenerateContestManagerLoginCode);
    rpc amuletActivityFetchInfo (ReqAmuletActivityFetchInfo) returns (ResAmuletActivityFetchInfo);
    rpc amuletActivityFetchBrief (ReqAmuletActivityFetchBrief) returns (ResAmuletActivityFetchBrief);
    rpc amuletActivityStartGame (ReqAmuletActivityStartGame) returns (ResAmuletActivityStartGame);
    rpc amuletActivityOperate (ReqAmuletActivityOperate) returns (ResAmuletActivityOperate);
    rpc amuletActivityChangeHands (ReqAmuletActivityChangeHands) returns (ResAmuletActivityChangeHands);
    rpc amuletActivityUpgrade (ReqAmuletActivityUpgrade) returns (ResAmuletActivityUpgrade);
    rpc amuletActivityBuy (ReqAmuletActivityBuy) returns (ResAmuletActivityBuy);
    rpc amuletActivitySelectPack (ReqAmuletActivitySelectPack) returns (ResAmuletActivitySelectPack);
    rpc amuletActivitySellEffect (ReqAmuletActivitySellEffect) returns (ResAmuletActivitySellEffect);
    rpc amuletActivityEffectSort (ReqAmuletActivityEffectSort) returns (ResCommon);
    rpc amuletActivityGiveup (ReqAmuletActivityGiveup) returns (ResCommon);
    rpc amuletActivityRefreshShop (ReqAmuletActivityRefreshShop) returns (ResAmuletActivityRefreshShop);
    rpc amuletActivitySelectFreeEffect (ReqAmuletActivitySelectFreeEffect) returns (ResAmuletActivitySelectFreeEffect);
    rpc amuletActivityUpgradeShopBuff (ReqAmuletActivityUpgradeShopBuff) returns (ResAmuletActivityUpgradeShopBuff);
    rpc amuletActivityEndShopping (ReqAmuletActivityEndShopping) returns (ResAmuletActivityEndShopping);
    rpc amuletActivitySetSkillLevel (ReqAmuletActivitySetSkillLevel) returns (ResCommon);
    rpc amuletActivityMaintainInfo (ReqCommon) returns (ResAmuletActivityMaintainInfo);
    rpc amuletActivitySelectRewardPack (ReqAmuletActivitySelectRewardPack) returns (ResAmuletActivitySelectRewardPack);
    rpc amuletActivityReceiveTaskReward (ReqAmuletActivityReceiveTaskReward) returns (ResAmuletActivityReceiveTaskReward);
    rpc storyActivityUnlock (ReqStoryActivityUnlock) returns (ResCommon);
    rpc storyActivityUnlockEnding (ReqStoryActivityUnlockEnding) returns (ResCommon);
    rpc storyActivityReceiveEndingReward (ReqStoryActivityReceiveEndingReward) returns (ResStoryReward);
    rpc storyActivityReceiveFinishReward (ReqStoryActivityReceiveFinishReward) returns (ResStoryReward);
    rpc storyActivityReceiveAllFinishReward (ReqStoryActivityReceiveAllFinishReward) returns (ResStoryReward);
    rpc storyActivityUnlockEndingAndReceive (ReqStoryActivityUnlockEndingAndReceive) returns (ResStoryActivityUnlockEndingAndReceive);
    rpc fetchActivityRank (ReqFetchActivityRank) returns (ResFetchActivityRank);
    rpc setVerifiedHidden (ReqSetVerifiedHidden) returns (ResCommon);
    rpc fetchQuestionnaireList (ReqFetchQuestionnaireList) returns (ResFetchQuestionnaireList);
    rpc fetchQuestionnaireDetail (ReqFetchQuestionnaireDetail) returns (ResFetchQuestionnaireDetail);
    rpc submitQuestionnaire (ReqSubmitQuestionnaire) returns (ResCommon);
    rpc setFriendRoomRandomBotChar (ReqSetFriendRoomRandomBotChar) returns (ResCommon);
    rpc fetchAccountGameHuRecords (ReqFetchAccountGameHuRecords) returns (ResFetchAccountGameHuRecords);
    rpc fetchAccountInfoExtra (ReqFetchAccountInfoExtra) returns (ResFetchAccountInfoExtra);
    rpc setAccountFavoriteHu (ReqSetAccountFavoriteHu) returns (ResCommon);
    rpc fetchSeerReport (ReqFetchSeerReport) returns (ResFetchSeerReport);
    rpc createSeerReport (ReqCreateSeerReport) returns (ResCreateSeerReport);
    rpc fetchSeerReportList (ReqCommon) returns (ResFetchSeerReportList);
    rpc fetchSeerInfo (ReqCommon) returns (ResFetchSeerInfo);
    rpc selectChestChooseUpActivity (ReqSelectChestChooseUp) returns (ReqCommon);
    rpc generateAnnualReportToken (ReqGenerateAnnualReportToken) returns (ResGenerateAnnualReportToken);
    rpc fetchAnnualReportInfo (ReqCommon) returns (ResFetchAnnualReportInfo);
    rpc remarkFriend (ReqRemarkFriend) returns (ResCommon);
    rpc simV2ActivityFetchInfo (ReqSimV2ActivityFetchInfo) returns (ResSimV2ActivityFetchInfo);
    rpc simV2ActivityStartSeason (ReqSimV2ActivityStartSeason) returns (ResSimV2ActivityStartSeason);
    rpc simV2ActivityTrain (ReqSimV2ActivityTrain) returns (ResSimV2ActivityTrain);
    rpc simV2ActivitySelectEvent (ReqSimV2ActivitySelectEvent) returns (ResSimV2ActivitySelectEvent);
    rpc simV2ActivityStartMatch (ReqSimV2ActivityStartMatch) returns (ResSimV2ActivityStartMatch);
    rpc simV2ActivityEndMatch (ReqSimV2ActivityEndMatch) returns (ResSimV2ActivityEndMatch);
    rpc simV2ActivityGiveUp (ReqSimV2ActivityGiveUp) returns (ResCommon);
    rpc simV2ActivitySetUpgrade (ReqSimV2ActivitySetUpgrade) returns (ResCommon);
}

message ResConnectionInfo {

    Error error = 1;
    NetworkEndpoint client_endpoint = 2;
}

message ResFetchQueueInfo {

    Error error = 1;
    uint32 remain = 2;
    uint32 rank = 3;
}

message ReqOpenidCheck {

    uint32 type = 1;
    string token = 2;
}

message ReqSignupAccount {

    string account = 1;
    string password = 2;
    string code = 3;
    uint32 type = 4;
    ClientDeviceInfo device = 5;
    string client_version_string = 6;
    string tag = 7;
}

message ResSignupAccount {

    Error error = 1;
}

message ReqLogin {

    string account = 1;
    string password = 2;
    bool reconnect = 3;
    ClientDeviceInfo device = 4;
    string random_key = 5;
    ClientVersionInfo client_version = 6;
    bool gen_access_token = 7;
    repeated uint32 currency_platforms = 8;
    uint32 type = 9;
    uint32 version = 10;
    string client_version_string = 11;
    string tag = 12;
}

message ResLogin {

    Error error = 1;
    uint32 account_id = 2;
    Account account = 3;
    GameConnectInfo game_info = 4;
    bool has_unread_announcement = 5;
    string access_token = 6;
    uint32 signup_time = 7;
    bool is_id_card_authed = 8;
    string country = 9;
    repeated uint32 logined_version = 10;
    repeated uint32 rewarded_version = 11;
}

message ReqPrepareLogin {

    string access_token = 1;
}

message ResFastLogin {

    Error error = 1;
    GameConnectInfo game_info = 2;
    Room room = 3;
}

message ReqEmailLogin {

    string email = 1;
    string password = 2;
    bool reconnect = 3;
    ClientDeviceInfo device = 4;
    string random_key = 5;
    string client_version = 6;
    bool gen_access_token = 7;
    repeated uint32 currency_platforms = 8;
}

message ReqBindAccount {

    string account = 1;
    string password = 2;
}

message ReqCreatePhoneVerifyCode {

    string phone = 1;
    uint32 usage = 2;
}

message ReqCreateEmailVerifyCode {

    string email = 1;
    uint32 usage = 2;
}

message ReqVerifyCodeForSecure {

    string code = 1;
    uint32 operation = 2;
}

message ResVerfiyCodeForSecure {

    Error error = 1;
    string secure_token = 2;
}

message ReqBindPhoneNumber {

    string code = 1;
    string phone = 2;
    string password = 3;
    bool multi_bind_version = 4;
}

message ReqUnbindPhoneNumber {

    string code = 1;
    string phone = 2;
    string password = 3;
}

message ResFetchPhoneLoginBind {

    Error error = 1;
    uint32 phone_login = 2;
}

message ReqCreatePhoneLoginBind {

    string password = 1;
}

message ReqBindEmail {

    string email = 1;
    string code = 2;
    string password = 3;
}

message ReqModifyPassword {

    string new_password = 1;
    string old_password = 2;
    string secure_token = 3;
}

message ReqOauth2Auth {

    uint32 type = 1;
    string code = 2;
    string uid = 3;
    string client_version_string = 4;
}

message ResOauth2Auth {

    Error error = 1;
    string access_token = 2;
}

message ReqOauth2Check {

    uint32 type = 1;
    string access_token = 2;
}

message ResOauth2Check {

    Error error = 1;
    bool has_account = 2;
}

message ReqOauth2Signup {

    uint32 type = 1;
    string access_token = 2;
    string email = 3;
    string advertise_str = 4;
    ClientDeviceInfo device = 5;
    ClientVersionInfo client_version = 6;
    string client_version_string = 7;
    string tag = 8;
}

message ResOauth2Signup {

    Error error = 1;
}

message ReqOauth2Login {

    uint32 type = 1;
    string access_token = 2;
    bool reconnect = 3;
    ClientDeviceInfo device = 4;
    string random_key = 5;
    ClientVersionInfo client_version = 6;
    bool gen_access_token = 7;
    repeated uint32 currency_platforms = 8;
    uint32 version = 9;
    string client_version_string = 10;
    string tag = 11;
}

message ReqDMMPreLogin {

    string finish_url = 1;
}

message ResDMMPreLogin {

    Error error = 2;
    string parameter = 1;
}

message ReqLogout {
}

message ResLogout {

    Error error = 1;
}

message ReqHeatBeat {

    uint32 no_operation_counter = 1;
}

message ReqSearchAccountByEidLobby {

    uint32 eid = 1;
}

message ResSearchAccountbyEidLobby {

    Error error = 1;
    uint32 account_id = 2;
}

message ReqLoginBeat {

    string contract = 1;
}

message ReqJoinMatchQueue {

    uint32 match_mode = 1;
    string client_version_string = 2;
}

message ReqCancelMatchQueue {

    uint32 match_mode = 1;
}

message ReqAccountInfo {

    uint32 account_id = 1;
}

message ResAccountInfo {

    Error error = 1;
    lq.Account account = 2;
    Room room = 3;
}

message ReqCreateNickname {

    string nickname = 1;
    string advertise_str = 2;
    string tag = 3;
}

message ReqModifyNickname {

    string nickname = 1;
    uint32 use_item_id = 2;
}

message ReqModifyBirthday {

    int32 birthday = 1;
}

message ResSelfRoom {

    Error error = 1;
    Room room = 2;
}

message ResFetchGamingInfo {

    Error error = 1;
    GameConnectInfo game_info = 2;
}

message ReqCreateRoom {

    uint32 player_count = 1;
    GameMode mode = 2;
    bool public_live = 3;
    string client_version_string = 4;
    string pre_rule = 5;
}

message ResCreateRoom {

    Error error = 1;
    Room room = 2;
}

message ReqJoinRoom {

    uint32 room_id = 1;
    string client_version_string = 2;
}

message ResJoinRoom {

    Error error = 1;
    Room room = 2;
}

message ReqRoomReady {

    bool ready = 1;
}

message ReqRoomDressing {

    bool dressing = 1;
}

message ReqRoomStart {
}

message ReqRoomKickPlayer {

    uint32 id = 1;
}

message ReqModifyRoom {

    uint32 robot_count = 1;
}

message ReqAddRoomRobot {

    uint32 position = 1;
}

message ReqChangeAvatar {

    uint32 avatar_id = 1;
}

message ReqAccountStatisticInfo {

    uint32 account_id = 1;
}

message ResAccountStatisticInfo {

    Error error = 1;
    repeated AccountStatisticData statistic_data = 2;
    AccountDetailStatisticV2 detail_data = 3;
}

message ResAccountChallengeRankInfo {

    Error error = 1;
    repeated ChallengeRank season_info = 2;

    message ChallengeRank {

        uint32 season = 1;
        uint32 rank = 2;
        uint32 level = 3;
    }
}

message ResAccountCharacterInfo {

    Error error = 2;
    repeated uint32 unlock_list = 1;
}

message ReqShopPurchase {

    string type = 1;
    uint32 id = 2;
}

message ResShopPurchase {

    Error error = 1;
    AccountUpdate update = 2;
}

message ReqGameRecord {

    string game_uuid = 1;
    string client_version_string = 2;
}

message ResGameRecord {

    Error error = 1;
    RecordGame head = 3;
    bytes data = 4;
    string data_url = 5;
}

message ReqGameRecordList {

    uint32 start = 1;
    uint32 count = 2;
    uint32 type = 3;
}

message ResGameRecordList {

    Error error = 1;
    uint32 total_count = 2;
    repeated RecordGame record_list = 3;
}

message ReqGameRecordListV2 {

    uint32 tag = 1;
    uint32 begin_time = 2;
    uint32 end_time = 3;
    repeated uint32 ranks = 4;
    repeated uint32 modes = 5;
    uint32 max_hu_type = 6;
    repeated uint32 level_mode = 7;
}

message ResGameRecordListV2 {

    Error error = 1;
    string iterator = 2;
    uint32 iterator_expire = 3;
    uint32 actual_begin_time = 4;
    uint32 actual_end_time = 5;
}

message ReqNextGameRecordList {

    string iterator = 1;
    uint32 count = 2;
}

message ResNextGameRecordList {

    Error error = 1;
    bool next = 2;
    repeated RecordListEntry entries = 3;
    uint32 iterator_expire = 4;
    uint32 next_end_time = 5;
}

message ResCollectedGameRecordList {

    Error error = 1;
    repeated RecordCollectedData record_list = 2;
    uint32 record_collect_limit = 3;
}

message ReqGameRecordsDetail {

    repeated string uuid_list = 1;
}

message ResGameRecordsDetail {

    Error error = 1;
    repeated RecordGame record_list = 2;
}

message ReqGameRecordsDetailV2 {

    repeated string uuid_list = 1;
}

message ResGameRecordsDetailV2 {

    Error error = 1;
    repeated RecordListEntry entries = 2;
}

message ReqAddCollectedGameRecord {

    string uuid = 1;
    string remarks = 2;
    uint32 start_time = 3;
    uint32 end_time = 4;
}

message ResAddCollectedGameRecord {

    Error error = 1;
}

message ReqRemoveCollectedGameRecord {

    string uuid = 1;
}

message ResRemoveCollectedGameRecord {

    Error error = 1;
}

message ReqChangeCollectedGameRecordRemarks {

    string uuid = 1;
    string remarks = 2;
}

message ResChangeCollectedGameRecordRemarks {

    Error error = 1;
}

message ReqLevelLeaderboard {

    uint32 type = 1;
}

message ResLevelLeaderboard {

    Error error = 1;
    repeated Item items = 2;
    uint32 self_rank = 3;

    message Item {

        uint32 account_id = 1;
        AccountLevel level = 2;
    }
}

message ReqChallangeLeaderboard {

    uint32 season = 1;
}

message ResChallengeLeaderboard {

    Error error = 1;
    repeated Item items = 2;
    uint32 self_rank = 3;

    message Item {

        uint32 account_id = 1;
        uint32 level = 2;
        string nickname = 3;
    }
}

message ReqMutiChallengeLevel {

    repeated uint32 account_id_list = 1;
    uint32 season = 2;
}

message ResMutiChallengeLevel {

    Error error = 1;
    repeated Item items = 2;

    message Item {

        uint32 account_id = 1;
        uint32 level = 2;
    }
}

message ReqMultiAccountId {

    repeated uint32 account_id_list = 1;
}

message ResMultiAccountBrief {

    Error error = 1;
    repeated PlayerBaseView players = 2;
}

message ResFriendList {

    Error error = 1;
    repeated Friend friends = 2;
    uint32 friend_max_count = 3;
    uint32 friend_count = 4;
}

message ResFriendApplyList {

    Error error = 1;
    repeated FriendApply applies = 2;

    message FriendApply {

        uint32 account_id = 1;
        uint32 apply_time = 2;
    }
}

message ReqApplyFriend {

    uint32 target_id = 1;
}

message ReqHandleFriendApply {

    uint32 target_id = 1;
    uint32 method = 2;
}

message ReqRemoveFriend {

    uint32 target_id = 1;
}

message ReqSearchAccountByPattern {

    bool search_next = 1;
    string pattern = 2;
}

message ResSearchAccountByPattern {

    Error error = 1;
    bool is_finished = 2;
    repeated uint32 match_accounts = 3;
    uint32 decode_id = 4;
}

message ReqAccountList {

    repeated uint32 account_id_list = 1;
}

message ResAccountStates {

    Error error = 1;
    repeated AccountActiveState states = 2;
}

message ReqSearchAccountById {

    uint32 account_id = 1;
}

message ResSearchAccountById {

    Error error = 1;
    PlayerBaseView player = 2;
}

message ResBagInfo {

    Error error = 1;
    Bag bag = 2;
}

message ReqUseBagItem {

    uint32 item_id = 1;
}

message ReqOpenManualItem {

    uint32 item_id = 1;
    uint32 count = 2;
    uint32 select_id = 3;
}

message ReqOpenRandomRewardItem {

    uint32 item_id = 1;
    uint32 count = 2;
}

message ResOpenRandomRewardItem {

    Error error = 1;
    repeated OpenResult results = 2;
}

message ReqOpenAllRewardItem {

    uint32 item_id = 1;
}

message ResOpenAllRewardItem {

    Error error = 1;
    repeated OpenResult results = 2;
}

message ReqComposeShard {

    uint32 item_id = 1;
}

message ReqFetchAnnouncement {

    string lang = 1;
    string platform = 2;
}

message ResAnnouncement {

    Error error = 1;
    repeated Announcement announcements = 2;
    repeated uint32 sort = 3;
    repeated uint32 read_list = 4;
}

message ResMailInfo {

    Error error = 1;
    repeated Mail mails = 2;
}

message ReqReadMail {

    uint32 mail_id = 1;
}

message ReqDeleteMail {

    uint32 mail_id = 1;
}

message ReqTakeAttachment {

    uint32 mail_id = 1;
}

message ReqReceiveAchievementGroupReward {

    uint32 group_id = 1;
}

message ResReceiveAchievementGroupReward {

    Error error = 1;
    repeated ExecuteReward execute_reward = 2;
}

message ReqReceiveAchievementReward {

    uint32 achievement_id = 1;
}

message ResReceiveAchievementReward {

    Error error = 1;
    repeated ExecuteReward execute_reward = 2;
}

message ResFetchAchievementRate {

    Error error = 2;
    repeated AchievementRate rate = 1;

    message AchievementRate {

        uint32 id = 1;
        uint32 rate = 2;
    }
}

message ResAchievement {

    Error error = 1;
    repeated AchievementProgress progresses = 2;
    repeated uint32 rewarded_group = 3;
}

message ResTitleList {

    Error error = 1;
    repeated uint32 title_list = 2;
}

message ReqUseTitle {

    uint32 title = 1;
}

message ReqBuyShiLian {

    uint32 type = 1;
}

message ReqUpdateClientValue {

    uint32 key = 1;
    uint32 value = 2;
}

message ResClientValue {

    Error error = 3;
    repeated Value datas = 1;
    uint32 recharged_count = 2;

    message Value {

        uint32 key = 1;
        uint32 value = 2;
    }
}

message ReqClientMessage {

    uint32 timestamp = 1;
    string message = 2;
}

message ReqCurrentMatchInfo {

    repeated uint32 mode_list = 1;
}

message ResCurrentMatchInfo {

    Error error = 1;
    repeated CurrentMatchInfo matches = 2;

    message CurrentMatchInfo {

        uint32 mode_id = 1;
        uint32 playing_count = 2;
    }
}

message ReqUserComplain {

    uint32 target_id = 1;
    uint32 type = 2;
    string content = 3;
    string game_uuid = 4;
    GameRoundInfo round_info = 5;

    message GameRoundInfo {

        uint32 chang = 1;
        uint32 ju = 2;
        uint32 ben = 3;
        uint32 seat = 4;
        uint32 xun = 5;
    }
}

message ReqReadAnnouncement {

    uint32 announcement_id = 1;
    repeated uint32 announcement_list = 2;
}

message ResReviveCoinInfo {

    Error error = 1;
    bool has_gained = 2;
}

message ResDailyTask {

    Error error = 1;
    repeated TaskProgress progresses = 2;
    bool has_refresh_count = 3;
    uint32 max_daily_task_count = 4;
    uint32 refresh_count = 5;
}

message ReqRefreshDailyTask {

    uint32 task_id = 1;
}

message ResRefreshDailyTask {

    Error error = 1;
    TaskProgress progress = 2;
    uint32 refresh_count = 3;
}

message ReqUseGiftCode {

    string code = 1;
}

message ResUseGiftCode {

    Error error = 1;
    repeated RewardSlot rewards = 6;
}

message ResUseSpecialGiftCode {

    Error error = 1;
    repeated ExecuteReward rewards = 2;
}

message ReqSendClientMessage {

    uint32 target_id = 1;
    uint32 type = 2;
    string content = 3;
}

message ReqGameLiveInfo {

    string game_uuid = 1;
}

message ResGameLiveInfo {

    Error error = 1;
    uint32 left_start_seconds = 2;
    GameLiveHead live_head = 3;
    repeated GameLiveSegmentUri segments = 4;
    uint32 now_millisecond = 5;
}

message ReqGameLiveLeftSegment {

    string game_uuid = 1;
    uint32 last_segment_id = 2;
}

message ResGameLiveLeftSegment {

    Error error = 1;
    uint32 live_state = 2;
    repeated GameLiveSegmentUri segments = 4;
    uint32 now_millisecond = 5;
    uint32 segment_end_millisecond = 6;
}

message ReqGameLiveList {

    uint32 filter_id = 1;
}

message ResGameLiveList {

    Error error = 1;
    repeated GameLiveHead live_list = 2;
}

message ResCommentSetting {

    Error error = 1;
    uint32 comment_allow = 2;
}

message ReqUpdateCommentSetting {

    uint32 comment_allow = 1;
}

message ReqFetchCommentList {

    uint32 target_id = 1;
}

message ResFetchCommentList {

    Error error = 1;
    uint32 comment_allow = 2;
    repeated uint32 comment_id_list = 3;
    uint32 last_read_id = 4;
}

message ReqFetchCommentContent {

    uint32 target_id = 1;
    repeated uint32 comment_id_list = 2;
}

message ResFetchCommentContent {

    Error error = 1;
    repeated CommentItem comments = 2;
}

message ReqLeaveComment {

    uint32 target_id = 1;
    string content = 2;
}

message ReqDeleteComment {

    uint32 target_id = 1;
    repeated uint32 delete_list = 2;
}

message ReqUpdateReadComment {

    uint32 read_id = 1;
}

message ResFetchRollingNotice {

    Error error = 2;
    RollingNotice notice = 3;
}

message ResFetchMaintainNotice {

    Error error = 1;
    MaintainNotice notice = 2;
}

message ReqFetchRollingNotice {

    string lang = 1;
}

message ResServerTime {

    uint32 server_time = 1;
    Error error = 2;
}

message ReqPlatformBillingProducts {

    uint32 shelves_id = 1;
}

message ResPlatformBillingProducts {

    Error error = 1;
    repeated BillingProduct products = 2;
}

message ReqCreateBillingOrder {

    uint32 goods_id = 1;
    uint32 payment_platform = 2;
    uint32 client_type = 3;
    uint32 account_id = 4;
    string client_version_string = 5;
}

message ResCreateBillingOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqSolveGooglePlayOrder {

    string inapp_purchase_data = 2;
    string inapp_data_signature = 3;
}

message ReqSolveGooglePlayOrderV3 {

    string order_id = 1;
    string transaction_id = 2;
    string token = 3;
    uint32 account_id = 4;
}

message ReqCancelGooglePlayOrder {

    string order_id = 1;
}

message ReqCreateWechatNativeOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string account_ip = 4;
    string client_version_string = 5;
}

message ResCreateWechatNativeOrder {

    Error error = 1;
    string qrcode_buffer = 2;
    string order_id = 3;
}

message ReqCreateWechatAppOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string account_ip = 4;
    string client_version_string = 5;
}

message ResCreateWechatAppOrder {

    Error error = 1;
    CallWechatAppParam call_wechat_app_param = 2;

    message CallWechatAppParam {

        string appid = 1;
        string partnerid = 2;
        string prepayid = 3;
        string package = 4;
        string noncestr = 5;
        string timestamp = 6;
        string sign = 7;
    }
}

message ReqCreateAlipayOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string alipay_trade_type = 4;
    string return_url = 5;
    string client_version_string = 6;
}

message ResCreateAlipayOrder {

    Error error = 1;
    string alipay_url = 2;
}

message ReqCreateAlipayScanOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string client_version_string = 4;
}

message ResCreateAlipayScanOrder {

    Error error = 1;
    string qrcode_buffer = 2;
    string order_id = 3;
    string qr_code = 4;
}

message ReqCreateAlipayAppOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string client_version_string = 4;
}

message ResCreateAlipayAppOrder {

    Error error = 1;
    string alipay_url = 2;
}

message ReqCreateJPCreditCardOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string return_url = 4;
    string access_token = 5;
    string client_version_string = 6;
}

message ResCreateJPCreditCardOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqCreateJPPaypalOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string return_url = 4;
    string access_token = 5;
    string client_version_string = 6;
}

message ResCreateJPPaypalOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqCreateJPAuOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string return_url = 4;
    string access_token = 5;
    string client_version_string = 6;
}

message ResCreateJPAuOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqCreateJPDocomoOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string return_url = 4;
    string access_token = 5;
    string client_version_string = 6;
}

message ResCreateJPDocomoOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqCreateJPWebMoneyOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string return_url = 4;
    string access_token = 5;
    string client_version_string = 6;
}

message ResCreateJPWebMoneyOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqCreateJPSoftbankOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string return_url = 4;
    string access_token = 5;
    string client_version_string = 6;
}

message ResCreateJPSoftbankOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqCreateJPPayPayOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string return_url = 4;
    string access_token = 5;
    string client_version_string = 6;
}

message ResCreateJPPayPayOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqFetchJPCommonCreditCardOrder {

    string order_id = 1;
    uint32 account_id = 2;
}

message ResFetchJPCommonCreditCardOrder {

    Error error = 1;
}

message ReqCreateJPGMOOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string return_url = 4;
    string access_token = 5;
    string client_version_string = 6;
}

message ResCreateJPGMOOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqCreateYostarOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    uint32 order_type = 4;
    string client_version_string = 5;
}

message ResCreateYostarOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqCreateENPaypalOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string return_url = 4;
    string access_token = 5;
    string client_version_string = 6;
}

message ResCreateENPaypalOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqCreateENJCBOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string return_url = 4;
    string access_token = 5;
    string client_version_string = 6;
}

message ResCreateENJCBOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqCreateENMasterCardOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string return_url = 4;
    string access_token = 5;
    string client_version_string = 6;
}

message ResCreateENMasterCardOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqCreateENVisaOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string return_url = 4;
    string access_token = 5;
    string client_version_string = 6;
}

message ResCreateENVisaOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqCreateENAlipayOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string return_url = 4;
    string access_token = 5;
    string client_version_string = 6;
}

message ResCreateENAlipayOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqCreateKRPaypalOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string return_url = 4;
    string access_token = 5;
    string client_version_string = 6;
}

message ResCreateKRPaypalOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqCreateKRJCBOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string return_url = 4;
    string access_token = 5;
    string client_version_string = 6;
}

message ResCreateKRJCBOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqCreateKRMasterCardOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string return_url = 4;
    string access_token = 5;
    string client_version_string = 6;
}

message ResCreateKRMasterCardOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqCreateKRVisaOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string return_url = 4;
    string access_token = 5;
    string client_version_string = 6;
}

message ResCreateKRVisaOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqCreateKRAlipayOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string return_url = 4;
    string access_token = 5;
    string client_version_string = 6;
}

message ResCreateKRAlipayOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqCreateDMMOrder {

    uint32 goods_id = 1;
    uint32 account_id = 2;
    uint32 client_type = 3;
    string client_version_string = 4;
}

message ResCreateDmmOrder {

    Error error = 1;
    string order_id = 2;
    string transaction_id = 3;
    string dmm_user_id = 4;
    string token = 5;
    string callback_url = 6;
    string request_time = 9;
    string dmm_app_id = 10;
}

message ReqCreateIAPOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string access_token = 4;
    string debt_order_id = 5;
    string client_version_string = 6;
}

message ResCreateIAPOrder {

    Error error = 1;
    string order_id = 2;
}

message ReqVerificationIAPOrder {

    string order_id = 1;
    string transaction_id = 2;
    string receipt_data = 3;
    uint32 account_id = 4;
}

message ResVerificationIAPOrder {

    Error error = 1;
}

message ReqCreateSteamOrder {

    string language = 1;
    uint32 account_id = 2;
    uint32 client_type = 3;
    uint32 goods_id = 4;
    string steam_id = 5;
    string debt_order_id = 6;
    string client_version_string = 7;
}

message ResCreateSteamOrder {

    Error error = 1;
    string order_id = 2;
    string platform_order_id = 3;
}

message ResRandomCharacter {

    Error error = 1;
    bool enabled = 2;
    repeated RandomCharacter pool = 3;
}

message ReqRandomCharacter {

    bool enabled = 1;
    repeated RandomCharacter pool = 2;
}

message ReqVerifySteamOrder {

    string order_id = 1;
    uint32 account_id = 2;
}

message ReqCreateMyCardOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string debt_order_id = 4;
    string client_version_string = 5;
}

message ResCreateMyCardOrder {

    Error error = 1;
    string auth_code = 2;
    string order_id = 3;
}

message ReqVerifyMyCardOrder {

    string order_id = 1;
    uint32 account_id = 2;
}

message ReqCreatePaypalOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    string debt_order_id = 4;
    string client_version_string = 5;
}

message ResCreatePaypalOrder {

    Error error = 1;
    string order_id = 2;
    string url = 3;
}

message ReqCreateXsollaOrder {

    uint32 goods_id = 1;
    uint32 client_type = 2;
    uint32 account_id = 3;
    uint32 payment_method = 4;
    string debt_order_id = 5;
    string client_version_string = 6;
    string account_ip = 7;
}

message ResCreateXsollaOrder {

    Error error = 1;
    string order_id = 2;
    string url = 3;
}

message ReqDeliverAA32Order {

    uint32 account_id = 1;
    string nsa_id = 2;
    string nsa_token = 3;
}

message ReqOpenChest {

    uint32 chest_id = 1;
    uint32 count = 2;
    bool use_ticket = 3;
    uint32 choose_up_activity_id = 4;
}

message ResOpenChest {

    Error error = 1;
    repeated OpenResult results = 2;
    uint32 total_open_count = 3;
    uint32 faith_count = 4;
    repeated ChestReplaceCountData chest_replace_up = 5;

    message ChestReplaceCountData {

        uint32 id = 1;
        uint32 count = 2;
    }
}

message ReqBuyFromChestShop {

    uint32 goods_id = 1;
    uint32 count = 2;
}

message ResBuyFromChestShop {

    Error error = 1;
    uint32 chest_id = 2;
    uint32 consume_count = 3;
    int32 faith_count = 4;
}

message ResDailySignInInfo {

    Error error = 1;
    uint32 sign_in_days = 2;
}

message ReqDoActivitySignIn {

    uint32 activity_id = 2;
}

message ResDoActivitySignIn {

    Error error = 1;
    repeated RewardData rewards = 2;
    uint32 sign_in_count = 3;

    message RewardData {

        uint32 resource_id = 1;
        uint32 count = 2;
    }
}

message ResCharacterInfo {

    Error error = 1;
    repeated Character characters = 2;
    repeated uint32 skins = 3;
    uint32 main_character_id = 4;
    uint32 send_gift_count = 5;
    uint32 send_gift_limit = 6;
    repeated uint32 finished_endings = 7;
    repeated uint32 rewarded_endings = 8;
    repeated uint32 character_sort = 9;
    repeated uint32 hidden_characters = 10;
    repeated uint32 other_character_sort = 11;
}

message ReqUpdateCharacterSort {

    repeated uint32 sort = 1;
    repeated uint32 other_sort = 2;
    repeated uint32 hidden_characters = 3;
}

message ReqChangeMainCharacter {

    uint32 character_id = 1;
}

message ReqChangeCharacterSkin {

    uint32 character_id = 1;
    uint32 skin = 2;
}

message ReqChangeCharacterView {

    uint32 character_id = 1;
    uint32 slot = 2;
    uint32 item_id = 3;
}

message ReqSetHiddenCharacter {

    repeated uint32 chara_list = 1;
}

message ResSetHiddenCharacter {

    Error error = 1;
    repeated uint32 hidden_characters = 2;
}

message ReqSendGiftToCharacter {

    uint32 character_id = 1;
    repeated Gift gifts = 2;

    message Gift {

        uint32 item_id = 1;
        uint32 count = 2;
    }
}

message ResSendGiftToCharacter {

    Error error = 1;
    uint32 level = 2;
    uint32 exp = 3;
}

message ReqSellItem {

    repeated Item sells = 1;

    message Item {

        uint32 item_id = 1;
        uint32 count = 2;
    }
}

message ResCommonView {

    Error error = 1;
    repeated Slot slots = 2;

    message Slot {

        uint32 slot = 1;
        uint32 value = 2;
    }
}

message ReqChangeCommonView {

    uint32 slot = 1;
    uint32 value = 2;
}

message ReqSaveCommonViews {

    repeated ViewSlot views = 1;
    uint32 save_index = 2;
    uint32 is_use = 3;
    string name = 4;
}

message ReqCommonViews {

    uint32 index = 1;
}

message ResCommonViews {

    Error error = 2;
    repeated ViewSlot views = 1;
    string name = 3;
}

message ResAllcommonViews {

    repeated Views views = 1;
    uint32 use = 2;
    Error error = 3;

    message Views {

        repeated ViewSlot values = 1;
        uint32 index = 2;
        string name = 3;
    }
}

message ReqUseCommonView {

    uint32 index = 3;
}

message ReqUpgradeCharacter {

    uint32 character_id = 1;
}

message ResUpgradeCharacter {

    Error error = 1;
    Character character = 2;
}

message ReqFinishedEnding {

    uint32 character_id = 1;
    uint32 story_id = 2;
    uint32 ending_id = 3;
}

message ReqGMCommand {

    string command = 1;
}

message ResShopInfo {

    Error error = 1;
    ShopInfo shop_info = 2;
}

message ReqBuyFromShop {

    uint32 goods_id = 1;
    uint32 count = 2;
    repeated Item ver_price = 3;
    repeated Item ver_goods = 4;

    message Item {

        uint32 id = 1;
        uint32 count = 2;
    }
}

message ResBuyFromShop {

    Error error = 1;
    repeated RewardSlot rewards = 2;
}

message ReqBuyFromZHP {

    uint32 goods_id = 1;
    uint32 count = 2;
}

message ReqPayMonthTicket {

    uint32 ticket_id = 1;
}

message ResPayMonthTicket {

    Error error = 1;
    uint32 resource_id = 2;
    uint32 resource_count = 3;
}

message ReqReshZHPShop {

    uint32 free_refresh = 1;
    uint32 cost_refresh = 2;
}

message ResRefreshZHPShop {

    Error error = 1;
    ZHPShop zhp = 2;
}

message ResMonthTicketInfo {

    Error error = 2;
    MonthTicketInfo month_ticket_info = 1;
}

message ReqExchangeCurrency {

    uint32 id = 1;
    uint32 count = 2;
}

message ResServerSettings {

    Error error = 2;
    ServerSettings settings = 1;
}

message ResAccountSettings {

    Error error = 1;
    repeated AccountSetting settings = 2;
}

message ReqUpdateAccountSettings {

    AccountSetting setting = 1;
}

message ResModNicknameTime {

    Error error = 2;
    uint32 last_mod_time = 1;
}

message ResMisc {

    Error error = 1;
    repeated uint32 recharged_list = 2;
    repeated MiscFaithData faiths = 3;
    uint32 verified_hidden = 4;
    uint32 verified_value = 5;
    uint32 disable_room_random_bot_char = 6;

    message MiscFaithData {

        uint32 faith_id = 1;
        int32 count = 2;
    }
}

message ReqModifySignature {

    string signature = 1;
}

message ResIDCardInfo {

    Error error = 1;
    bool is_authed = 2;
    string country = 3;
}

message ReqUpdateIDCardInfo {

    string fullname = 1;
    string card_no = 2;
}

message ResVipReward {

    Error error = 1;
    repeated uint32 gained_vip_levels = 2;
}

message ResFetchRefundOrder {

    Error error = 1;
    repeated OrderInfo orders = 2;
    uint32 clear_deadline = 3;
    repeated I18nContext message = 4;

    message OrderInfo {

        uint32 success_time = 1;
        uint32 goods_id = 2;
        uint32 cleared = 3;
        string order_id = 4;
    }
}

message ReqGainVipReward {

    uint32 vip_level = 1;
}

message ReqFetchCustomizedContestList {

    uint32 start = 1;
    uint32 count = 2;
    string lang = 3;
}

message ResFetchCustomizedContestList {

    Error error = 1;
    repeated CustomizedContestBase contests = 2;
    repeated CustomizedContestBase follow_contests = 3;
}

message ReqFetchCustomizedContestAuthInfo {

    uint32 unique_id = 1;
}

message ResFetchCustomizedContestAuthInfo {

    Error error = 1;
    uint32 observer_level = 2;
}

message ReqEnterCustomizedContest {

    uint32 unique_id = 1;
    string lang = 2;
}

message ResEnterCustomizedContest {

    Error error = 1;
    CustomizedContestDetail detail_info = 2;
    CustomizedContestPlayerReport player_report = 3;
    bool is_followed = 4;
    uint32 state = 5;
    bool is_admin = 6;
}

message ReqFetchCustomizedContestOnlineInfo {

    uint32 unique_id = 1;
}

message ResFetchCustomizedContestOnlineInfo {

    Error error = 1;
    uint32 online_player = 2;
}

message ReqFetchCustomizedContestByContestId {

    uint32 contest_id = 1;
    string lang = 2;
}

message ResFetchCustomizedContestByContestId {

    Error error = 1;
    CustomizedContestAbstract contest_info = 2;
}

message ReqSignupCustomizedContest {

    uint32 unique_id = 1;
    string client_version_string = 2;
}

message ResSignupCustomizedContest {

    Error error = 1;
    uint32 state = 2;
}

message ReqStartCustomizedContest {

    uint32 unique_id = 1;
    string client_version_string = 2;
}

message ReqStopCustomizedContest {

    uint32 unique_id = 1;
}

message ReqJoinCustomizedContestChatRoom {

    uint32 unique_id = 1;
}

message ResJoinCustomizedContestChatRoom {

    Error error = 1;
    string token = 2;
}

message ReqSayChatMessage {

    string content = 1;
    uint32 unique_id = 2;
}

message ReqFetchCustomizedContestGameLiveList {

    uint32 unique_id = 1;
}

message ResFetchCustomizedContestGameLiveList {

    Error error = 1;
    repeated GameLiveHead live_list = 2;
}

message ReqFetchCustomizedContestGameRecords {

    uint32 unique_id = 1;
    uint32 last_index = 2;
    uint32 season_id = 3;
}

message ResFetchCustomizedContestGameRecords {

    Error error = 1;
    uint32 next_index = 2;
    repeated RecordGame record_list = 3;
}

message ReqTargetCustomizedContest {

    uint32 unique_id = 1;
}

message ResActivityList {

    Error error = 1;
    repeated Activity activities = 2;
}

message ResAccountActivityData {

    Error error = 1;
    repeated ExchangeRecord exchange_records = 2;
    repeated TaskProgress task_progress_list = 3;
    repeated ActivityAccumulatedPointData accumulated_point_list = 4;
    repeated ActivityRankPointData rank_data_list = 5;
    repeated TaskProgress flip_task_progress_list = 6;
    repeated ActivitySignInData sign_in_data = 7;
    repeated ActivityRichmanData richman_data = 8;
    repeated TaskProgress period_task_progress_list = 9;
    repeated TaskProgress random_task_progress_list = 10;
    repeated ChestUpData chest_up_data = 11;
    ActivitySNSData sns_data = 12;
    repeated lq.MineActivityData mine_data = 13;
    repeated lq.RPGActivity rpg_data = 14;
    repeated lq.ActivityArenaData arena_data = 15;
    repeated lq.FeedActivityData feed_data = 16;
    repeated lq.SegmentTaskProgress segment_task_progress_list = 17;
    repeated lq.VoteData vote_records = 18;
    repeated lq.ActivitySpotData spot_data = 19;
    repeated lq.ActivityFriendGiftData friend_gift_data = 20;
    repeated lq.ActivityUpgradeData upgrade_data = 21;
    repeated lq.ActivityGachaUpdateData gacha_data = 22;
    repeated lq.ActivitySimulationData simulation_data = 23;
    repeated lq.ActivityCombiningLQData combining_data = 24;
    repeated lq.ActivityVillageData village_data = 25;
    repeated lq.ActivityFestivalData festival_data = 26;
    repeated lq.ActivityIslandData island_data = 27;
    repeated lq.ActivityStoryData story_data = 29;
    repeated lq.ActivityChooseUpData choose_up_data = 30;

    message ActivitySignInData {

        uint32 activity_id = 1;
        uint32 sign_in_count = 2;
        uint32 last_sign_in_time = 3;
    }

    message BuffData {

        uint32 type = 1;
        uint32 remain = 2;
        uint32 effect = 3;
    }

    message ActivityRichmanData {

        uint32 activity_id = 1;
        uint32 location = 2;
        uint32 finished_count = 3;
        uint32 chest_position = 4;
        uint32 bank_save = 5;
        uint32 exp = 6;
        repeated BuffData buff = 7;
    }

    message ChestUpData {

        uint32 id = 1;
        uint32 count = 2;
    }

    message ActivitySNSData {

        repeated SNSBlog blog = 1;
        repeated uint32 liked_id = 2;
        repeated SNSReply reply = 3;
    }
}

message SNSBlog {

    uint32 id = 1;
    uint32 read_time = 2;
}

message SNSReply {

    uint32 id = 1;
    uint32 reply_time = 2;
}

message ReqExchangeActivityItem {

    uint32 exchange_id = 1;
    uint32 count = 2;
}

message ResExchangeActivityItem {

    Error error = 1;
    repeated ExecuteReward execute_reward = 2;
}

message ReqCompleteActivityTask {

    uint32 task_id = 1;
}

message ReqCompleteActivityTaskBatch {

    repeated uint32 task_list = 1;
}

message ReqCompletePeriodActivityTaskBatch {

    repeated uint32 task_list = 1;
}

message ReqReceiveActivityFlipTask {

    uint32 task_id = 1;
}

message ResReceiveActivityFlipTask {

    uint32 count = 1;
    Error error = 2;
}

message ReqCompleteSegmentTaskReward {

    uint32 task_id = 1;
    uint32 count = 2;
}

message ResCompleteSegmentTaskReward {

    Error error = 1;
    repeated ExecuteReward rewards = 2;
}

message ReqFetchActivityFlipInfo {

    uint32 activity_id = 1;
}

message ResFetchActivityFlipInfo {

    repeated uint32 rewards = 1;
    uint32 count = 2;
    Error error = 3;
}

message ReqGainAccumulatedPointActivityReward {

    uint32 activity_id = 1;
    uint32 reward_id = 2;
}

message ReqGainMultiPointActivityReward {

    uint32 activity_id = 1;
    repeated uint32 reward_id_list = 2;
}

message ReqFetchRankPointLeaderboard {

    uint32 leaderboard_id = 1;
}

message ResFetchRankPointLeaderboard {

    Error error = 1;
    repeated Item items = 2;
    uint32 last_refresh_time = 3;

    message Item {

        uint32 account_id = 1;
        uint32 rank = 2;
        PlayerBaseView view = 3;
        uint32 point = 4;
    }
}

message ReqGainRankPointReward {

    uint32 leaderboard_id = 1;
    uint32 activity_id = 2;
}

message ReqRichmanNextMove {

    uint32 activity_id = 1;
}

message ResRichmanNextMove {

    repeated PathData paths = 1;
    uint32 dice = 2;
    uint32 location = 3;
    uint32 finished_count = 4;
    uint32 step = 5;
    repeated BuffData buff = 6;
    uint32 bank_save = 7;
    uint32 chest_position = 8;
    uint32 exp = 9;
    uint32 bank_save_add = 10;
    Error error = 11;

    message RewardData {

        uint32 resource_id = 1;
        uint32 count = 2;
        uint32 origin_count = 3;
        uint32 type = 5;
    }

    message PathData {

        uint32 location = 1;
        repeated RewardData rewards = 2;
        repeated uint32 events = 3;
    }

    message BuffData {

        uint32 type = 1;
        uint32 remain = 2;
        uint32 effect = 3;
    }
}

message ReqRichmanSpecialMove {

    uint32 activity_id = 1;
    uint32 step = 2;
}

message ReqRichmanChestInfo {

    uint32 activity_id = 1;
}

message ResRichmanChestInfo {

    repeated ItemData items = 1;
    Error error = 2;

    message ItemData {

        uint32 id = 1;
        uint32 count = 2;
    }
}

message ReqCreateGameObserveAuth {

    string game_uuid = 1;
}

message ResCreateGameObserveAuth {

    Error error = 1;
    string token = 2;
    string location = 3;
}

message ReqRefreshGameObserveAuth {

    string token = 1;
}

message ResRefreshGameObserveAuth {

    Error error = 1;
    uint32 ttl = 2;
}

message ResActivityBuff {

    Error error = 1;
    repeated lq.ActivityBuffData buff_list = 2;
}

message ReqUpgradeActivityBuff {

    uint32 buff_id = 1;
}

message ReqUpgradeActivityLevel {

    uint32 activity_id = 1;
    uint32 group = 2;
    uint32 count = 3;
}

message ResUpgradeActivityLevel {

    Error error = 1;
    repeated ExecuteReward rewards = 2;
}

message ReqReceiveUpgradeActivityReward {

    uint32 activity_id = 1;
}

message ResReceiveUpgradeActivityReward {

    Error error = 1;
    repeated ExecuteReward rewards = 2;
}

message ReqReceiveAllActivityGift {

    uint32 activity_id = 1;
}

message ResReceiveAllActivityGift {

    Error error = 1;
    repeated ExecuteReward rewards = 2;
    repeated ReceiveRewards receive_gift = 3;

    message ReceiveRewards {

        uint32 id = 1;
        uint32 from_account_id = 2;
        uint32 item_id = 3;
        uint32 count = 4;
    }
}

message ResUpgradeChallenge {

    Error error = 1;
    repeated TaskProgress task_progress = 2;
    uint32 refresh_count = 3;
    uint32 level = 4;
    uint32 match_count = 5;
    uint32 ticket_id = 6;
}

message ResRefreshChallenge {

    Error error = 1;
    repeated TaskProgress task_progress = 2;
    uint32 refresh_count = 3;
    uint32 level = 4;
    uint32 match_count = 5;
    uint32 ticket_id = 6;
}

message ResFetchChallengeInfo {

    Error error = 1;
    repeated TaskProgress task_progress = 2;
    uint32 refresh_count = 3;
    uint32 level = 4;
    uint32 match_count = 5;
    uint32 ticket_id = 6;
    repeated uint32 rewarded_season = 7;
}

message ReqForceCompleteChallengeTask {

    uint32 task_id = 1;
}

message ResFetchABMatch {

    Error error = 1;
    uint32 match_id = 2;
    uint32 match_count = 3;
    uint32 buy_in_count = 4;
    uint32 point = 5;
    bool rewarded = 6;
    repeated MatchPoint match_max_point = 7;
    bool quit = 8;

    message MatchPoint {

        uint32 match_id = 1;
        uint32 point = 2;
    }
}

message ReqStartUnifiedMatch {

    string match_sid = 1;
    string client_version_string = 2;
}

message ReqCancelUnifiedMatch {

    string match_sid = 1;
}

message ResChallengeSeasonInfo {

    Error error = 2;
    repeated ChallengeInfo challenge_season_list = 1;

    message ChallengeInfo {

        uint32 season_id = 1;
        uint32 start_time = 2;
        uint32 end_time = 3;
        uint32 state = 4;
    }
}

message ReqReceiveChallengeRankReward {

    uint32 season_id = 1;
}

message ResReceiveChallengeRankReward {

    Error error = 2;
    repeated Reward rewards = 1;

    message Reward {

        uint32 resource_id = 1;
        uint32 count = 2;
    }
}

message ReqBuyInABMatch {

    uint32 match_id = 1;
}

message ReqGamePointRank {

    uint32 activity_id = 1;
}

message ResGamePointRank {

    Error error = 1;
    repeated RankInfo rank = 2;
    uint32 self_rank = 3;

    message RankInfo {

        uint32 account_id = 1;
        uint32 point = 2;
    }
}

message ResFetchSelfGamePointRank {

    Error error = 1;
    uint32 self_rate = 2;
}

message ReqReadSNS {

    uint32 id = 1;
}

message ResReadSNS {

    Error error = 1;
    SNSBlog sns_content = 2;
}

message ReqReplySNS {

    uint32 id = 1;
}

message ResReplySNS {

    Error error = 1;
    SNSReply sns_reply = 2;
}

message ReqLikeSNS {

    uint32 id = 1;
}

message ResLikeSNS {

    Error error = 1;
    uint32 is_liked = 2;
}

message ReqDigMine {

    uint32 activity_id = 1;
    Point point = 2;
}

message ResDigMine {

    Error error = 1;
    repeated MineReward map = 2;
    repeated lq.RewardSlot reward = 3;
}

message ReqFetchLastPrivacy {

    repeated uint32 type = 1;
}

message ResFetchLastPrivacy {

    Error error = 1;
    repeated PrivacyInfo privacy = 2;

    message PrivacyInfo {

        uint32 type = 1;
        string version = 2;
    }
}

message ReqCheckPrivacy {

    string device_type = 1;
    repeated Versions versions = 2;

    message Versions {

        string version = 1;
        uint32 type = 3;
    }
}

message ReqFetchRPGBattleHistory {

    uint32 activity_id = 1;
}

message ResFetchRPGBattleHistory {

    Error error = 1;
    repeated BattleResult battle_result = 2;
    lq.RPGState start_state = 3;
    lq.RPGState current_state = 4;

    message BattleResult {

        string uuid = 14;
        uint32 chang = 1;
        uint32 ju = 2;
        uint32 ben = 3;
        uint32 target = 4;
        uint32 damage = 5;
        uint32 heal = 6;
        uint32 monster_seq = 7;
        uint32 chain_atk = 8;
        uint32 killed = 9;
        uint32 is_luk = 10;
        uint32 is_dex = 11;
        uint32 is_extra = 12;
        string reward = 13;
        uint32 points = 15;
        uint32 is_zimo = 16;
    }
}

message ResFetchRPGBattleHistoryV2 {

    Error error = 1;
    repeated BattleResultV2 battle_result = 2;
    lq.RPGState start_state = 3;
    lq.RPGState current_state = 4;
    repeated BattleResultV2 recent_battle_result = 5;

    message BattleResultV2 {

        string uuid = 14;
        uint32 chang = 1;
        uint32 ju = 2;
        uint32 ben = 3;
        uint32 damage = 5;
        uint32 monster_seq = 7;
        uint32 killed = 9;
        repeated lq.ActivityBuffData buff = 10;
        uint32 points = 11;
    }
}

message ReqBuyArenaTicket {

    uint32 activity_id = 1;
}

message ReqArenaReward {

    uint32 activity_id = 1;
}

message ReqEnterArena {

    uint32 activity_id = 1;
}

message ResArenaReward {

    Error error = 1;
    repeated RewardItem items = 2;

    message RewardItem {

        uint32 id = 1;
        uint32 count = 2;
    }
}

message ReqReceiveRPGRewards {

    uint32 activity_id = 1;
}

message ReqReceiveRPGReward {

    uint32 activity_id = 1;
    uint32 monster_seq = 2;
}

message ResReceiveRPGRewards {

    Error error = 1;
    repeated RewardItem items = 2;

    message RewardItem {

        uint32 id = 1;
        uint32 count = 2;
    }
}

message ReqFetchOBToken {

    string uuid = 1;
}

message ResFetchOBToken {

    Error error = 1;
    string token = 2;
    uint32 create_time = 3;
    uint32 delay = 4;
    uint32 start_time = 5;
}

message ReqReceiveCharacterRewards {

    uint32 character_id = 1;
    uint32 level = 2;
}

message ResReceiveCharacterRewards {

    Error error = 1;
    repeated RewardItem items = 2;

    message RewardItem {

        uint32 id = 1;
        uint32 count = 2;
    }
}

message ReqFeedActivityFeed {

    uint32 activity_id = 1;
    uint32 count = 2;
}

message ResFeedActivityFeed {

    Error error = 1;
    repeated RewardItem items = 2;
    uint32 feed_count = 3;

    message RewardItem {

        uint32 id = 1;
        uint32 count = 2;
    }
}

message ReqSendActivityGiftToFriend {

    uint32 activity_id = 1;
    uint32 item_id = 2;
    uint32 target_id = 3;
}

message ResSendActivityGiftToFriend {

    Error error = 1;
    uint32 send_gift_count = 2;
}

message ReqReceiveActivityGift {

    uint32 activity_id = 1;
    uint32 id = 2;
}

message ReqFetchFriendGiftActivityData {

    uint32 activity_id = 1;
    repeated uint32 account_list = 2;
}

message ResFetchFriendGiftActivityData {

    Error error = 1;
    repeated FriendData list = 2;

    message ItemCountData {

        uint32 item = 1;
        uint32 count = 2;
    }

    message FriendData {

        uint32 account_id = 1;
        repeated ItemCountData items = 2;
        uint32 receive_count = 3;
    }
}

message ReqOpenPreChestItem {

    uint32 item_id = 1;
    uint32 pool_id = 2;
}

message ResOpenPreChestItem {

    Error error = 1;
    repeated OpenResult results = 2;
}

message ReqFetchVoteActivity {

    uint32 activity_id = 1;
}

message ResFetchVoteActivity {

    Error error = 1;
    repeated uint32 vote_rank = 2;
    uint32 update_time = 3;
}

message ReqVoteActivity {

    uint32 vote = 1;
    uint32 activity_id = 2;
}

message ResVoteActivity {

    Error error = 1;
    repeated VoteData vote_records = 2;
}

message ReqUnlockActivitySpot {

    uint32 unique_id = 1;
}

message ReqUnlockActivitySpotEnding {

    uint32 unique_id = 1;
    uint32 ending_id = 2;
}

message ReqReceiveActivitySpotReward {

    uint32 unique_id = 1;
}

message ResReceiveActivitySpotReward {

    Error error = 1;
    repeated RewardItem items = 2;

    message RewardItem {

        uint32 id = 1;
        uint32 count = 2;
    }
}

message ReqLogReport {

    uint32 success = 1;
    uint32 failed = 2;
}

message ReqBindOauth2 {

    uint32 type = 1;
    string token = 2;
}

message ReqFetchOauth2 {

    uint32 type = 1;
}

message ResFetchOauth2 {

    Error error = 1;
    string openid = 2;
}

message ResDeleteAccount {

    Error error = 1;
    uint32 delete_time = 2;
}

message ReqSetLoadingImage {

    repeated uint32 images = 1;
}

message ResFetchShopInterval {

    Error error = 1;
    repeated ShopInterval result = 2;

    message ShopInterval {

        uint32 group_id = 1;
        uint32 interval = 2;
    }
}

message ResFetchActivityInterval {

    Error error = 1;
    repeated ActivityInterval result = 2;

    message ActivityInterval {

        uint32 activity_id = 1;
        uint32 interval = 2;
    }
}

message ResFetchrecentFriend {

    Error error = 1;
    repeated uint32 account_list = 2;
}

message ReqOpenGacha {

    uint32 activity_id = 1;
    uint32 count = 2;
}

message ResOpenGacha {

    Error error = 1;
    repeated uint32 result_list = 2;
    repeated ExecuteReward reward_items = 3;
    repeated ExecuteReward sp_reward_items = 4;
    uint32 remain_count = 5;
}

message ReqTaskRequest {

    repeated uint32 params = 1;
}

message ReqSimulationActivityTrain {

    uint32 activity_id = 1;
    uint32 type = 2;
}

message ResSimulationActivityTrain {

    Error error = 1;
    uint32 result_type = 2;
    repeated uint32 final_stats = 4;
}

message ReqFetchSimulationGameRecord {

    string game_uuid = 1;
    uint32 activity_id = 2;
}

message ResFetchSimulationGameRecord {

    Error error = 1;
    repeated ActivitySimulationGameRecordMessage messages = 2;
}

message ReqStartSimulationActivityGame {

    uint32 activity_id = 1;
}

message ResStartSimulationActivityGame {

    Error error = 1;
    repeated lq.ActivitySimulationGameRecord records = 2;
}

message ReqFetchSimulationGameRank {

    uint32 activity_id = 1;
    uint32 day = 2;
}

message ResFetchSimulationGameRank {

    Error error = 1;
    repeated RankInfo rank = 2;

    message RankInfo {

        uint32 character = 1;
        float score = 2;
    }
}

message ReqGenerateCombiningCraft {

    uint32 activity_id = 1;
    uint32 bin_id = 2;
}

message ResGenerateCombiningCraft {

    Error error = 1;
    uint32 pos = 2;
    uint32 craft_id = 3;
}

message ReqMoveCombiningCraft {

    uint32 activity_id = 1;
    uint32 from = 2;
    uint32 to = 3;
}

message ResMoveCombiningCraft {

    Error error = 1;
    uint32 pos = 2;
    uint32 combined = 3;
    uint32 craft_id = 4;
    BonusData bonus = 5;

    message BonusData {

        uint32 craft_id = 1;
        uint32 pos = 2;
    }
}

message ReqCombiningRecycleCraft {

    uint32 activity_id = 1;
    uint32 pos = 2;
}

message ResCombiningRecycleCraft {

    Error error = 1;
    repeated ExecuteReward reward_items = 2;
}

message ReqRecoverCombiningRecycle {

    uint32 activity_id = 1;
}

message ResRecoverCombiningRecycle {

    Error error = 1;
    uint32 craft_id = 2;
    uint32 pos = 3;
}

message ReqFinishCombiningOrder {

    uint32 activity_id = 1;
    uint32 craft_pos = 2;
    uint32 order_pos = 3;
}

message ResFinishCombiningOrder {

    Error error = 1;
    repeated ExecuteReward reward_items = 2;
}

message ResFetchInfo {

    Error error = 1;
    ResServerTime server_time = 2;
    ResServerSettings server_setting = 3;
    ResClientValue client_value = 4;
    ResFriendList friend_list = 5;
    ResFriendApplyList friend_apply_list = 6;
    ResFetchrecentFriend recent_friend = 7;
    ResMailInfo mail_info = 8;
    ResReviveCoinInfo receive_coin_info = 9;
    ResTitleList title_list = 10;
    ResBagInfo bag_info = 11;
    ResShopInfo shop_info = 12;
    ResFetchShopInterval shop_interval = 13;
    ResAccountActivityData activity_data = 14;
    ResFetchActivityInterval activity_interval = 15;
    ResActivityBuff activity_buff = 16;
    ResVipReward vip_reward = 17;
    ResMonthTicketInfo month_ticket_info = 18;
    ResAchievement achievement = 19;
    ResCommentSetting comment_setting = 20;
    ResAccountSettings account_settings = 21;
    ResModNicknameTime mod_nickname_time = 22;
    ResMisc misc = 23;
    ResAnnouncement announcement = 24;
    ResActivityList activity_list = 26;
    ResCharacterInfo character_info = 27;
    ResAllcommonViews all_common_views = 28;
    ResCollectedGameRecordList collected_game_record_list = 29;
    ResFetchMaintainNotice maintain_notice = 30;
    ResRandomCharacter random_character = 31;
    ResFetchServerMaintenanceInfo maintenance_info = 32;
    ResFetchSeerInfo seer_info = 33;
    ResFetchAnnualReportInfo annual_report_info = 34;
}

message ResFetchSeerInfo {

    Error error = 1;
    uint32 remain_count = 2;
    uint32 date_limit = 3;
    uint32 expire_time = 4;
}

message ResFetchServerMaintenanceInfo {

    repeated ServerFunctionMaintenanceInfo function_maintenance = 1;

    message ServerFunctionMaintenanceInfo {

        string name = 1;
        bool open = 2;
    }
}

message ReqUpgradeVillageBuilding {

    uint32 building_id = 1;
    uint32 activity_id = 2;
}

message ReqReceiveVillageBuildingReward {

    uint32 activity_id = 1;
    uint32 building_id = 2;
    repeated RewardSlot rewards = 3;
}

message ResReceiveVillageBuildingReward {

    Error error = 1;
    repeated ExecuteReward reward_items = 2;
}

message ReqStartVillageTrip {

    uint32 dest = 1;
    uint32 activity_id = 2;
}

message ReqReceiveVillageTripReward {

    uint32 activity_id = 1;
    uint32 dest_id = 2;
    repeated RewardSlot rewards = 3;
}

message ResReceiveVillageTripReward {

    Error error = 1;
    repeated ExecuteReward reward_items = 2;
}

message ReqCompleteVillageTask {

    uint32 task_id = 1;
    uint32 activity_id = 2;
}

message ResCompleteVillageTask {

    Error error = 1;
    repeated ExecuteReward reward_items = 2;
}

message ReqGetFriendVillageData {

    repeated uint32 account_list = 1;
    uint32 activity_id = 2;
}

message ResGetFriendVillageData {

    Error error = 1;
    repeated FriendVillageData list = 2;

    message FriendVillageData {

        uint32 account_id = 1;
        uint32 level = 2;
    }
}

message ReqSetVillageWorker {

    uint32 building_id = 1;
    uint32 worker_pos = 2;
    uint32 activity_id = 3;
}

message ResSetVillageWorker {

    Error error = 1;
    VillageBuildingData building = 2;
    uint32 update_time = 3;
}

message ReqNextRoundVillage {

    uint32 activity_id = 1;
}

message ResNextRoundVillage {

    Error error = 1;
    ActivityVillageData activity_data = 2;
}

message ReqResolveFestivalActivityProposal {

    uint32 activity_id = 1;
    uint32 id = 2;
    uint32 select = 3;
}

message ResResolveFestivalActivityProposal {

    Error error = 1;
    repeated uint32 effected_buff = 2;
    uint32 result = 3;
    repeated ExecuteResult reward_items = 4;
    uint32 level = 5;
}

message ReqResolveFestivalActivityEvent {

    uint32 activity_id = 1;
    uint32 id = 2;
    uint32 select = 3;
}

message ResResolveFestivalActivityEvent {

    Error error = 1;
    repeated uint32 effected_buff = 2;
    repeated ExecuteResult reward_items = 4;
    uint32 ending_id = 5;
    uint32 level = 6;
}

message ReqBuyFestivalProposal {

    uint32 activity_id = 1;
}

message ResBuyFestivalProposal {

    Error error = 1;
    FestivalProposalData new_proposal = 2;
}

message ReqIslandActivityMove {

    uint32 activity_id = 1;
    uint32 zone_id = 2;
}

message ReqIslandActivityBuy {

    uint32 activity_id = 1;
    repeated BuyItems items = 2;

    message BuyItems {

        uint32 goods_id = 2;
        repeated uint32 pos = 3;
        uint32 rotate = 4;
        uint32 bag_id = 5;
        uint32 price = 6;
    }
}

message ReqIslandActivitySell {

    uint32 activity_id = 1;
    repeated SellItem items = 2;

    message SellItem {

        uint32 bag_id = 2;
        uint32 id = 3;
        uint32 price = 4;
    }
}

message ReqIslandActivityTidyBag {

    uint32 activity_id = 1;
    repeated BagData bag_data = 2;

    message BagData {

        uint32 bag_id = 2;
        repeated ITemData items = 3;
        repeated uint32 drops = 4;

        message ITemData {

            uint32 id = 1;
            repeated uint32 pos = 2;
            uint32 rotate = 3;
        }
    }
}

message ReqIslandActivityUnlockBagGrid {

    uint32 activity_id = 1;
    uint32 bag_id = 2;
    repeated uint32 pos = 3;
}

message ContestSetting {

    repeated LevelLimit level_limit = 1;
    uint32 game_limit = 2;
    uint32 system_broadcast = 3;

    message LevelLimit {

        uint32 type = 1;
        uint32 value = 2;
    }
}

message ReqCreateCustomizedContest {

    string name = 1;
    uint32 open_show = 2;
    GameMode game_rule_setting = 3;
    uint32 start_time = 4;
    uint32 end_time = 5;
    uint32 auto_match = 6;
    uint32 rank_rule = 7;
    ContestSetting contest_setting = 8;
}

message ResCreateCustomizedContest {

    Error error = 1;
    uint32 unique_id = 2;
}

message ReqFetchmanagerCustomizedContestList {

    string lang = 1;
}

message ResFetchManagerCustomizedContestList {

    Error error = 1;
    repeated CustomizedContestBase contests = 2;
}

message ReqFetchManagerCustomizedContest {

    uint32 unique_id = 1;
}

message ResFetchManagerCustomizedContest {

    Error error = 1;
    string name = 2;
    uint32 open_show = 3;
    GameMode game_rule_setting = 4;
    uint32 start_time = 5;
    uint32 end_time = 6;
    uint32 auto_match = 7;
    uint32 rank_rule = 8;
    uint32 check_state = 9;
    string checking_name = 10;
    ContestSetting contest_setting = 11;
}

message ReqUpdateManagerCustomizedContest {

    string name = 1;
    uint32 open_show = 2;
    GameMode game_rule_setting = 3;
    uint32 start_time = 4;
    uint32 end_time = 5;
    uint32 unique_id = 6;
    uint32 auto_match = 7;
    uint32 rank_rule = 8;
    ContestSetting contest_setting = 9;
}

message ReqFetchContestPlayerRank {

    uint32 unique_id = 1;
    uint32 limit = 2;
    uint32 offset = 3;
}

message ResFetchContestPlayerRank {

    Error error = 1;
    uint32 total = 2;
    repeated SeasonRank rank = 3;
    PlayerData player_data = 4;

    message ContestPlayerAccountData {

        uint32 total_game_count = 1;
        repeated ContestGameResult recent_games = 2;
        repeated ContestSeriesGameResult highest_series_points = 3;

        message ContestGameResult {

            uint32 rank = 1;
            int32 total_point = 2;
        }

        message ContestSeriesGameResult {

            uint32 key = 1;
            repeated ContestGameResult results = 2;
        }
    }

    message SeasonRank {

        uint32 account_id = 1;
        string nickname = 2;
        ContestPlayerAccountData data = 3;
    }

    message PlayerData {

        uint32 rank = 1;
        ContestPlayerAccountData data = 2;
    }
}

message ReqFetchReadyPlayerList {

    uint32 unique_id = 1;
}

message ResFetchReadyPlayerList {

    Error error = 1;
    repeated Player list = 2;

    message Player {

        uint32 account_id = 1;
        string nickname = 2;
    }
}

message ReqCreateGamePlan {

    uint32 unique_id = 1;
    repeated uint32 account_list = 2;
    uint32 game_start_time = 3;
    uint32 shuffle_seats = 4;
    uint32 ai_level = 5;
}

message ResGenerateContestManagerLoginCode {

    Error error = 1;
    string code = 2;
}

message ReqAmuletActivityFetchInfo {

    uint32 activity_id = 1;
}

message ResAmuletActivityFetchInfo {

    Error error = 1;
    ActivityAmuletData data = 2;
}

message ReqAmuletActivityFetchBrief {

    uint32 activity_id = 1;
}

message ResAmuletActivityFetchBrief {

    Error error = 1;
    ActivityAmuletUpgradeData upgrade = 4;
    ActivityAmuletIllustratedBookData illustrated_book = 5;
    ActivityAmuletTaskData task = 6;
}

message ReqAmuletActivityStartGame {

    uint32 activity_id = 1;
}

message ResAmuletActivityStartGame {

    Error error = 1;
    AmuletGameData game = 2;
}

message ReqAmuletActivityOperate {

    uint32 activity_id = 1;
    uint32 type = 2;
    repeated uint32 tile = 3;
}

message ResAmuletActivityOperate {

    Error error = 1;
    AmuletHuleOperateResult hu_result = 2;
    AmuletGangOperateResult gang_result = 3;
    AmuletDealTileResult deal_result = 4;
    AmuletUpgradeResult upgrade_result = 5;
    bool upgraded = 6;
    bool failed = 7;
    AmuletGameUpdateData game_update = 8;
    AmuletDiscardTileResult discard_result = 9;
    AmuletStartGameResult start_result = 10;
}

message ReqAmuletActivityChangeHands {

    uint32 activity_id = 1;
    repeated uint32 hands = 2;
}

message ResAmuletActivityChangeHands {

    Error error = 1;
    repeated uint32 hands = 2;
    uint32 remain_change_tile_count = 3;
    repeated AmuletActivityTingInfo ting_list = 4;
    repeated AmuletEffectData effect_list = 5;
}

message ReqAmuletActivityUpgrade {

    uint32 activity_id = 1;
}

message ResAmuletActivityUpgrade {

    Error error = 1;
    AmuletGameData game = 2;
    repeated AmuletActivityHookEffect hook_effect = 3;
}

message ReqAmuletActivitySelectPack {

    uint32 activity_id = 1;
    uint32 id = 2;
}

message ResAmuletActivitySelectPack {

    Error error = 1;
    repeated AmuletEffectData effect_list = 2;
    AmuletGameShopData shop = 3;
}

message ReqAmuletActivityBuy {

    uint32 activity_id = 1;
    uint32 id = 3;
}

message ResAmuletActivityBuy {

    Error error = 1;
    uint32 coin = 2;
    AmuletGameShopData shop = 3;
    uint32 stage = 4;
    repeated AmuletEffectData effect_list = 5;
    uint32 total_consumed_coin = 6;
}

message ReqAmuletActivitySellEffect {

    uint32 activity_id = 1;
    uint32 id = 2;
}

message ResAmuletActivitySellEffect {

    Error error = 1;
    uint32 coin = 2;
    repeated AmuletEffectData effect_list = 3;
    AmuletGameUpdateData game_update = 4;
    uint32 remain_change_tile_count = 5;
    repeated AmuletActivityHookEffect hook_effect = 6;
    AmuletGameShopData shop = 7;
}

message ReqAmuletActivityEffectSort {

    uint32 activity_id = 1;
    repeated uint32 sorted_id = 2;
}

message ReqAmuletActivityGiveup {

    uint32 activity_id = 1;
}

message ReqAmuletActivityRefreshShop {

    uint32 activity_id = 1;
}

message ResAmuletActivityRefreshShop {

    Error error = 1;
    AmuletGameShopData shop = 2;
    uint32 coin = 3;
    repeated AmuletEffectData effect_list = 4;
}

message ReqAmuletActivitySelectFreeEffect {

    uint32 activity_id = 1;
    uint32 selected_id = 2;
}

message ResAmuletActivitySelectFreeEffect {

    Error error = 1;
    AmuletGameUpdateData game_update = 3;
    uint32 remain_change_tile_count = 4;
    repeated uint32 locked_tile = 5;
    uint32 locked_tile_count = 6;
}

message ReqAmuletActivityUpgradeShopBuff {

    uint32 activity_id = 1;
    uint32 id = 2;
}

message ResAmuletActivityUpgradeShopBuff {

    Error error = 1;
    AmuletGameUpdateData game_update = 3;
    repeated AmuletEffectData shop_buff_list = 4;
    uint32 total_consumed_coin = 5;
}

message ReqAmuletActivityEndShopping {

    uint32 activity_id = 1;
}

message ResAmuletActivityEndShopping {

    Error error = 1;
    AmuletGameUpdateData game_update = 3;
}

message ReqAmuletActivitySetSkillLevel {

    uint32 activity_id = 1;
    repeated AmuletSkillData skill = 2;
}

message ResAmuletActivityMaintainInfo {

    Error error = 1;
    string mode = 2;
}

message ReqAmuletActivitySelectRewardPack {

    uint32 activity_id = 1;
    uint32 id = 2;
}

message ResAmuletActivitySelectRewardPack {

    Error error = 1;
    AmuletGameUpdateData game_update = 2;
    AmuletGameShopData shop = 3;
}

message ReqAmuletActivityReceiveTaskReward {

    uint32 activity_id = 1;
    repeated uint32 task_list = 2;
}

message ResAmuletActivityReceiveTaskReward {

    Error error = 1;
    ActivityAmuletTaskData task = 2;
}

message ReqStoryActivityUnlock {

    uint32 activity_id = 1;
    uint32 story_id = 2;
}

message ReqStoryActivityUnlockEnding {

    uint32 activity_id = 1;
    uint32 story_id = 2;
    uint32 ending_id = 3;
}

message ReqStoryActivityReceiveEndingReward {

    uint32 activity_id = 1;
    uint32 story_id = 2;
    uint32 ending_id = 3;
}

message ResStoryReward {

    Error error = 1;
    repeated ExecuteReward reward_items = 2;
}

message ReqStoryActivityReceiveFinishReward {

    uint32 activity_id = 1;
    uint32 story_id = 2;
}

message ReqStoryActivityReceiveAllFinishReward {

    uint32 activity_id = 1;
    uint32 story_id = 2;
}

message ReqStoryActivityUnlockEndingAndReceive {

    uint32 activity_id = 1;
    uint32 story_id = 2;
    uint32 ending_id = 3;
}

message ResStoryActivityUnlockEndingAndReceive {

    Error error = 1;
    repeated ExecuteReward ending_reward = 2;
    repeated ExecuteReward finish_reward = 3;
    repeated ExecuteReward all_finish_reward = 4;
}

message ReqFetchActivityRank {

    uint32 activity_id = 1;
    repeated uint32 account_list = 2;
}

message ResFetchActivityRank {

    Error error = 1;
    repeated ActivityRankItem items = 4;
    ActivityRankItem self = 5;

    message ActivityRankItem {

        uint32 account_id = 1;
        uint64 score = 2;
        string data = 3;
        uint32 rank = 4;
    }
}

message ReqFetchQuestionnaireList {

    string lang = 1;
    string channel = 2;
}

message ResFetchQuestionnaireList {

    Error error = 1;
    repeated QuestionnaireBrief list = 2;
    repeated uint32 finished_list = 3;
}

message ReqFetchQuestionnaireDetail {

    uint32 id = 1;
    string lang = 2;
    string channel = 3;
}

message ResFetchQuestionnaireDetail {

    Error error = 1;
    QuestionnaireDetail detail = 2;
}

message ReqSetVerifiedHidden {

    uint32 verified_hidden = 1;
}

message ReqSubmitQuestionnaire {

    uint32 questionnaire_id = 1;
    uint32 questionnaire_version_id = 2;
    repeated QuestionnaireAnswer answers = 3;
    uint32 open_time = 4;
    uint32 finish_time = 5;
    string client = 6;

    message QuestionnaireAnswer {

        uint32 question_id = 1;
        repeated QuestionnaireAnswerValue values = 2;

        message QuestionnaireAnswerValue {

            string value = 1;
            string custom_input = 2;
        }
    }
}

message ReqSetFriendRoomRandomBotChar {

    uint32 disable_random_char = 1;
}

message ReqFetchAccountGameHuRecords {

    string uuid = 1;
    uint32 category = 2;
    uint32 type = 3;
}

message ResFetchAccountGameHuRecords {

    Error error = 1;
    repeated GameHuRecords records = 2;

    message GameHuRecords {

        uint32 chang = 1;
        uint32 ju = 2;
        uint32 ben = 3;
        uint32 title_id = 4;
        repeated string hands = 5;
        repeated string ming = 6;
        string hupai = 7;
        repeated uint32 hu_fans = 8;
    }
}

message ReqFetchAccountInfoExtra {

    uint32 account_id = 1;
    uint32 category = 2;
    uint32 type = 3;
}

message ResFetchAccountInfoExtra {

    Error error = 1;
    repeated AccountInfoGameRecord recent_games = 2;
    repeated GameHuTypeDetail hu_type_details = 3;
    repeated AccountGameRankDetail game_rank_details = 4;

    message AccountInfoGameRecord {

        string uuid = 1;
        uint32 start_time = 2;
        uint32 end_time = 3;
        uint32 tag = 4;
        uint32 sub_tag = 5;
        uint32 rank = 6;
        uint32 final_point = 7;
        repeated AccountGameResult results = 8;

        message AccountGameResult {

            uint32 rank = 1;
            uint32 account_id = 2;
            string nickname = 3;
            uint32 verified = 4;
            int32 grading_score = 5;
            int32 final_point = 6;
            uint32 seat = 7;
            AccountLevel level = 8;
            AccountLevel level3 = 9;
        }
    }

    message GameHuTypeDetail {

        uint32 type = 1;
        uint32 count = 2;
    }

    message AccountGameRankDetail {

        uint32 rank = 1;
        uint32 count = 2;
    }
}

message ReqSetAccountFavoriteHu {

    uint32 mode = 1;
    uint32 category = 2;
    uint32 type = 3;
    string uuid = 4;
    uint32 chang = 5;
    uint32 ju = 6;
    uint32 ben = 7;
}

message ReqFetchSeerReport {

    string uuid = 1;
}

message ResFetchSeerReport {

    Error error = 1;
    SeerReport report = 2;
}

message ReqCreateSeerReport {

    string uuid = 1;
}

message ResCreateSeerReport {

    Error error = 1;
    SeerBrief seer_report = 2;
}

message ResFetchSeerReportList {

    Error error = 1;
    repeated SeerBrief seer_report_list = 2;
}

message ReqSelectChestChooseUp {

    uint32 activity_id = 1;
    uint32 selection = 2;
    uint32 chest_id = 3;
}

message ReqGenerateAnnualReportToken {

    string lang = 1;
}

message ResGenerateAnnualReportToken {

    Error error = 1;
    string token = 2;
    string url = 3;
}

message ResFetchAnnualReportInfo {

    Error error = 1;
    uint32 start_time = 2;
    uint32 end_time = 3;
}

message ReqRemarkFriend {

    uint32 account_id = 1;
    string remark = 2;
}

message ReqSimV2ActivityFetchInfo {

    uint32 activity_id = 1;
}

message ResSimV2ActivityFetchInfo {

    Error error = 1;
    SimulationV2Data data = 2;
}

message ReqSimV2ActivityStartSeason {

    uint32 activity_id = 1;
}

message ResSimV2ActivityStartSeason {

    Error error = 1;
    SimulationV2SeasonData season = 2;
}

message ReqSimV2ActivityTrain {

    uint32 activity_id = 1;
    uint32 ability = 2;
    uint32 skip = 3;
}

message ResSimV2ActivityTrain {

    Error error = 1;
    SimulationV2Event event = 2;
    SimulationV2Ability ability = 3;
    uint32 round = 4;
    repeated SimulationV2Effect effect_list = 5;
    uint32 train_result = 6;
    bool is_end = 7;
    SimulationV2Record record = 8;
}

message ReqSimV2ActivitySelectEvent {

    uint32 activity_id = 1;
    uint32 selection_id = 2;
}

message ResSimV2ActivitySelectEvent {

    Error error = 1;
    SimulationV2Event event = 2;
    SimulationV2Ability ability = 3;
    SimulationV2Match match = 4;
    repeated SimulationV2Effect effect_list = 5;
    uint32 round = 7;
    bool is_end = 8;
    uint32 result_id = 9;
    SimulationV2Record record = 10;
    repeated uint32 effected_buff_list = 11;
}

message ReqSimV2ActivityStartMatch {

    uint32 activity_id = 1;
}

message ResSimV2ActivityStartMatch {

    Error error = 1;
    SimulationV2Event event = 2;
    SimulationV2Match match = 4;
    repeated SimulationV2Effect effect_list = 5;
    bool is_match_end = 6;
}

message ReqSimV2ActivityEndMatch {

    uint32 activity_id = 1;
}

message ResSimV2ActivityEndMatch {

    Error error = 1;
    uint32 round = 2;
    bool is_end = 3;
    SimulationV2Record record = 4;
    int32 total_score = 5;
    repeated SimulationV2MatchRecord match_history = 6;
    repeated SimulationV2MatchReward rewards = 7;
    repeated SimulationV2Effect effect_list = 8;
    SimulationV2Ability ability = 9;

    message SimulationV2MatchReward {

        uint32 type = 1;
        repeated uint32 params = 2;
    }
}

message ReqSimV2ActivityGiveUp {

    uint32 activity_id = 1;
}

message ReqSimV2ActivitySetUpgrade {

    uint32 activity_id = 1;
    SimulationV2Ability upgrade = 2;
}
service FastTest {
    rpc authGame (ReqAuthGame) returns (ResAuthGame);
    rpc enterGame (ReqCommon) returns (ResEnterGame);
    rpc syncGame (ReqSyncGame) returns (ResSyncGame);
    rpc finishSyncGame (ReqCommon) returns (ResCommon);
    rpc terminateGame (ReqCommon) returns (ResCommon);
    rpc inputOperation (ReqSelfOperation) returns (ResCommon);
    rpc inputChiPengGang (ReqChiPengGang) returns (ResCommon);
    rpc confirmNewRound (ReqCommon) returns (ResCommon);
    rpc broadcastInGame (ReqBroadcastInGame) returns (ResCommon);
    rpc inputGameGMCommand (ReqGMCommandInGaming) returns (ResCommon);
    rpc fetchGamePlayerState (ReqCommon) returns (ResGamePlayerState);
    rpc checkNetworkDelay (ReqCommon) returns (ResCommon);
    rpc clearLeaving (ReqCommon) returns (ResCommon);
    rpc voteGameEnd (ReqVoteGameEnd) returns (ResGameEndVote);
    rpc authObserve (ReqAuthObserve) returns (ResCommon);
    rpc startObserve (ReqCommon) returns (ResStartObserve);
    rpc stopObserve (ReqCommon) returns (ResCommon);
}

message ReqAuthGame {

    uint32 account_id = 1;
    string token = 2;
    string game_uuid = 3;
    string session = 4;
    string gift = 5;
    uint32 vs = 6;
}

message ResAuthGame {

    Error error = 1;
    repeated PlayerGameView players = 2;
    repeated uint32 seat_list = 3;
    bool is_game_start = 4;
    GameConfig game_config = 5;
    repeated uint32 ready_id_list = 6;
    repeated PlayerGameView robots = 7;
}

message GameRestore {

    GameSnapshot snapshot = 1;
    repeated ActionPrototype actions = 2;
    uint32 passed_waiting_time = 3;
    uint32 game_state = 4;
    uint32 start_time = 5;
    uint32 last_pause_time_ms = 6;
}

message ResEnterGame {

    Error error = 1;
    bool is_end = 2;
    uint32 step = 3;
    GameRestore game_restore = 4;
}

message ReqSyncGame {

    string round_id = 1;
    uint32 step = 2;
}

message ResSyncGame {

    Error error = 1;
    bool is_end = 2;
    uint32 step = 3;
    GameRestore game_restore = 4;
}

message ReqSelfOperation {

    uint32 type = 1;
    uint32 index = 2;
    string tile = 3;
    bool cancel_operation = 4;
    bool moqie = 5;
    uint32 timeuse = 6;
    int32 tile_state = 7;
    repeated string change_tiles = 8;
    repeated int32 tile_states = 9;
    uint32 gap_type = 10;
}

message ReqChiPengGang {

    uint32 type = 1;
    uint32 index = 2;
    bool cancel_operation = 3;
    uint32 timeuse = 6;
}

message ReqBroadcastInGame {

    string content = 1;
    bool except_self = 2;
}

message ReqGMCommandInGaming {

    string json_data = 1;
}

message ResGamePlayerState {

    Error error = 1;
    repeated GamePlayerState state_list = 2;
}

message ReqVoteGameEnd {

    bool yes = 1;
}

message ResGameEndVote {

    bool success = 1;
    uint32 vote_cd_end_time = 2;
    Error error = 3;
}

message ReqAuthObserve {

    string token = 1;
}

message ResStartObserve {

    GameLiveHead head = 1;
    GameLiveSegment passed = 2;
}

message NotifyNewGame {

    string game_uuid = 1;
    repeated string player_list = 2;
}

message NotifyPlayerLoadGameReady {

    repeated uint32 ready_id_list = 1;
}

message NotifyGameBroadcast {

    uint32 seat = 1;
    string content = 2;
}

message NotifyGameEndResult {

    GameEndResult result = 1;
}

message NotifyGameTerminate {

    string reason = 1;
}

message NotifyPlayerConnectionState {

    uint32 seat = 1;
    GamePlayerState state = 2;
}

message NotifyAccountLevelChange {

    AccountLevel origin = 1;
    AccountLevel final = 2;
    uint32 type = 3;
}

message NotifyGameFinishReward {

    uint32 mode_id = 1;
    LevelChange level_change = 2;
    MatchChest match_chest = 3;
    MainCharacter main_character = 4;
    CharacterGift character_gift = 5;
    repeated BadgeAchieveProgress badges = 6;

    message LevelChange {

        AccountLevel origin = 1;
        AccountLevel final = 2;
        uint32 type = 3;
    }

    message MatchChest {

        uint32 chest_id = 1;
        uint32 origin = 2;
        uint32 final = 3;
        bool is_graded = 4;
        repeated RewardSlot rewards = 5;
    }

    message MainCharacter {

        uint32 level = 1;
        uint32 exp = 2;
        uint32 add = 3;
    }

    message CharacterGift {

        uint32 origin = 1;
        uint32 final = 2;
        uint32 add = 3;
        bool is_graded = 4;
    }
}

message NotifyActivityReward {

    repeated ActivityReward activity_reward = 1;

    message ActivityReward {

        uint32 activity_id = 1;
        repeated RewardSlot rewards = 2;
    }
}

message NotifyActivityPoint {

    repeated ActivityPoint activity_points = 1;

    message ActivityPoint {

        uint32 activity_id = 1;
        uint32 point = 2;
    }
}

message NotifyLeaderboardPoint {

    repeated LeaderboardPoint leaderboard_points = 1;

    message LeaderboardPoint {

        uint32 leaderboard_id = 1;
        uint32 point = 2;
    }
}

message NotifyGamePause {

    bool paused = 1;
}

message NotifyEndGameVote {

    repeated VoteResult results = 1;
    uint32 start_time = 2;
    uint32 duration_time = 3;

    message VoteResult {

        uint32 account_id = 1;
        bool yes = 2;
    }
}

message NotifyObserveData {

    GameLiveUnit unit = 1;
}

message ActionMJStart {
}

message NewRoundOpenedTiles {

    uint32 seat = 1;
    repeated string tiles = 2;
    repeated uint32 count = 3;
}

message MuyuInfo {

    uint32 seat = 1;
    uint32 count = 2;
    uint32 count_max = 3;
    uint32 id = 4;
}

message ChuanmaGang {

    repeated int32 old_scores = 1;
    repeated int32 delta_scores = 2;
    repeated int32 scores = 3;
    GameEnd gameend = 4;
    repeated HuleInfo hules_history = 5;
}

message YongchangInfo {

    uint32 seat = 1;
    uint32 moqie_count = 2;
    uint32 moqie_bonus = 3;
    uint32 shouqie_count = 4;
    uint32 shouqie_bonus = 5;
}

message ActionNewCard {

    uint32 field_spell = 1;
}

message RecordNewCard {

    uint32 field_spell = 1;
}

message ActionNewRound {

    uint32 chang = 1;
    uint32 ju = 2;
    uint32 ben = 3;
    repeated string tiles = 4;
    string dora = 5;
    repeated int32 scores = 6;
    OptionalOperationList operation = 7;
    uint32 liqibang = 8;
    repeated TingPaiDiscardInfo tingpais0 = 9;
    repeated TingPaiInfo tingpais1 = 10;
    bool al = 11;
    string md5 = 12;
    uint32 left_tile_count = 13;
    repeated string doras = 14;
    repeated NewRoundOpenedTiles opens = 15;
    MuyuInfo muyu = 16;
    uint32 ju_count = 17;
    uint32 field_spell = 18;
    string sha256 = 19;
    YongchangInfo yongchang = 20;
    string salt_sha256 = 21;
}

message RecordNewRound {

    uint32 chang = 1;
    uint32 ju = 2;
    uint32 ben = 3;
    string dora = 4;
    repeated int32 scores = 5;
    uint32 liqibang = 6;
    repeated string tiles0 = 7;
    repeated string tiles1 = 8;
    repeated string tiles2 = 9;
    repeated string tiles3 = 10;
    repeated TingPai tingpai = 11;
    OptionalOperationList operation = 12;
    string md5 = 13;
    string paishan = 14;
    uint32 left_tile_count = 15;
    repeated string doras = 16;
    repeated NewRoundOpenedTiles opens = 17;
    MuyuInfo muyu = 18;
    repeated OptionalOperationList operations = 19;
    uint32 ju_count = 20;
    uint32 field_spell = 21;
    string sha256 = 22;
    YongchangInfo yongchang = 23;
    string salt_sha256 = 24;
    string salt = 25;

    message TingPai {

        uint32 seat = 1;
        repeated TingPaiInfo tingpais1 = 2;
    }
}

message GameSnapshot {

    uint32 chang = 1;
    uint32 ju = 2;
    uint32 ben = 3;
    uint32 index_player = 4;
    uint32 left_tile_count = 5;
    repeated string hands = 6;
    repeated string doras = 7;
    uint32 liqibang = 8;
    repeated PlayerSnapshot players = 9;
    bool zhenting = 10;

    message PlayerSnapshot {

        int32 score = 1;
        int32 liqiposition = 2;
        uint32 tilenum = 3;
        repeated string qipais = 4;
        repeated Fulu mings = 5;

        message Fulu {

            uint32 type = 1;
            repeated string tile = 2;
            repeated uint32 from = 3;
        }
    }
}

message ActionPrototype {

    uint32 step = 1;
    string name = 2;
    bytes data = 3;
}

message GameDetailRecords {

    repeated bytes records = 1;
    uint32 version = 2;
    repeated GameAction actions = 3;
    bytes bar = 4;
}

message GameSelfOperation {

    uint32 type = 1;
    uint32 index = 2;
    string tile = 3;
    bool cancel_operation = 4;
    bool moqie = 5;
    uint32 timeuse = 6;
    int32 tile_state = 7;
    repeated string change_tiles = 8;
    repeated int32 tile_states = 9;
    uint32 gap_type = 10;
}

message GameChiPengGang {

    uint32 type = 1;
    uint32 index = 2;
    bool cancel_operation = 3;
    uint32 timeuse = 6;
}

message GameVoteGameEnd {

    bool yes = 1;
}

message GameUserInput {

    uint32 seat = 1;
    uint32 type = 2;
    uint32 emo = 3;
    GameSelfOperation operation = 10;
    GameChiPengGang cpg = 11;
    GameVoteGameEnd vote = 12;
}

message GameUserEvent {

    uint32 seat = 1;
    uint32 type = 2;
}

message GameAction {

    uint32 passed = 1;
    uint32 type = 2;
    bytes result = 3;
    GameUserInput user_input = 4;
    GameUserEvent user_event = 5;
    uint32 game_event = 6;
}

message OptionalOperation {

    uint32 type = 1;
    repeated string combination = 2;
    repeated string change_tiles = 3;
    repeated int32 change_tile_states = 4;
    uint32 gap_type = 5;
}

message OptionalOperationList {

    uint32 seat = 1;
    repeated OptionalOperation operation_list = 2;
    uint32 time_add = 4;
    uint32 time_fixed = 5;
}

message LiQiSuccess {

    uint32 seat = 1;
    int32 score = 2;
    uint32 liqibang = 3;
    bool failed = 4;
    uint32 liqi_type_beishuizhizhan = 5;
}

message FanInfo {

    string name = 1;
    uint32 val = 2;
    uint32 id = 3;
}

message HuleInfo {

    repeated string hand = 1;
    repeated string ming = 2;
    string hu_tile = 3;
    uint32 seat = 4;
    bool zimo = 5;
    bool qinjia = 6;
    bool liqi = 7;
    repeated string doras = 8;
    repeated string li_doras = 9;
    bool yiman = 10;
    uint32 count = 11;
    repeated FanInfo fans = 12;
    uint32 fu = 13;
    string title = 14;
    uint32 point_rong = 15;
    uint32 point_zimo_qin = 16;
    uint32 point_zimo_xian = 17;
    uint32 title_id = 18;
    uint32 point_sum = 19;
    uint32 dadian = 20;
    uint32 baopai = 21;
    repeated uint32 baopai_seats = 22;
    repeated string lines = 23;
    uint32 tianming_bonus = 24;
    repeated string baida_changed = 25;
    string hu_tile_bai_da_changed = 26;
}

message TingPaiInfo {

    string tile = 1;
    bool haveyi = 2;
    bool yiman = 3;
    uint32 count = 4;
    uint32 fu = 5;
    uint32 biao_dora_count = 6;
    bool yiman_zimo = 7;
    uint32 count_zimo = 8;
    uint32 fu_zimo = 9;
}

message TingPaiDiscardInfo {

    string tile = 1;
    bool zhenting = 2;
    repeated TingPaiInfo infos = 3;
}

message HunZhiYiJiBuffInfo {

    uint32 seat = 1;
    uint32 continue_deal_count = 2;
    bool overload = 3;
}

message GameEnd {

    repeated int32 scores = 1;
}

message ActionSelectGap {

    repeated uint32 gap_types = 1;
    repeated TingPaiDiscardInfo tingpais0 = 2;
    repeated TingPaiInfo tingpais1 = 3;
    OptionalOperationList operation = 4;
}

message RecordSelectGap {

    repeated uint32 gap_types = 1;
    repeated TingPai tingpai = 2;
    OptionalOperationList operation = 3;

    message TingPai {

        uint32 seat = 1;
        repeated TingPaiInfo tingpais1 = 2;
    }
}

message ActionChangeTile {

    repeated string in_tiles = 1;
    repeated int32 in_tile_states = 2;
    repeated string out_tiles = 3;
    repeated int32 out_tile_states = 4;
    repeated string doras = 5;
    repeated TingPaiDiscardInfo tingpais0 = 6;
    repeated TingPaiInfo tingpais1 = 7;
    OptionalOperationList operation = 8;
    uint32 change_type = 9;
}

message RecordChangeTile {

    repeated string doras = 1;
    repeated TingPai tingpai = 2;
    repeated ChangeTile change_tile_infos = 3;
    OptionalOperationList operation = 4;
    uint32 change_type = 5;
    repeated OptionalOperationList operations = 6;

    message TingPai {

        uint32 seat = 1;
        repeated TingPaiInfo tingpais1 = 2;
    }

    message ChangeTile {

        repeated string in_tiles = 1;
        repeated int32 in_tile_states = 2;
        repeated string out_tiles = 3;
        repeated int32 out_tile_states = 4;
    }
}

message ActionRevealTile {

    uint32 seat = 1;
    bool is_liqi = 2;
    bool is_wliqi = 3;
    bool moqie = 4;
    repeated int32 scores = 5;
    uint32 liqibang = 6;
    OptionalOperationList operation = 7;
    repeated TingPaiInfo tingpais = 8;
    string tile = 9;
    bool zhenting = 10;
}

message RecordRevealTile {

    uint32 seat = 1;
    bool is_liqi = 2;
    bool is_wliqi = 3;
    bool moqie = 4;
    repeated int32 scores = 5;
    uint32 liqibang = 6;
    repeated OptionalOperationList operations = 7;
    repeated TingPaiInfo tingpais = 8;
    string tile = 9;
    repeated bool zhenting = 10;
}

message ActionUnveilTile {

    int32 seat = 1;
    repeated int32 scores = 2;
    uint32 liqibang = 3;
    OptionalOperationList operation = 4;
}

message RecordUnveilTile {

    int32 seat = 1;
    repeated int32 scores = 2;
    uint32 liqibang = 3;
    OptionalOperationList operation = 4;
}

message ActionLockTile {

    uint32 seat = 1;
    repeated int32 scores = 2;
    uint32 liqibang = 3;
    string tile = 4;
    OptionalOperationList operation = 5;
    bool zhenting = 6;
    repeated TingPaiInfo tingpais = 7;
    repeated string doras = 8;
    int32 lock_state = 9;
}

message RecordLockTile {

    uint32 seat = 1;
    repeated int32 scores = 2;
    uint32 liqibang = 3;
    string tile = 4;
    repeated OptionalOperationList operation = 5;
    repeated bool zhentings = 6;
    repeated TingPaiInfo tingpais = 7;
    repeated string doras = 8;
    int32 lock_state = 9;
}

message ActionDiscardTile {

    uint32 seat = 1;
    string tile = 2;
    bool is_liqi = 3;
    OptionalOperationList operation = 4;
    bool moqie = 5;
    bool zhenting = 6;
    repeated TingPaiInfo tingpais = 7;
    repeated string doras = 8;
    bool is_wliqi = 9;
    uint32 tile_state = 10;
    MuyuInfo muyu = 11;
    bool revealed = 12;
    repeated int32 scores = 13;
    uint32 liqibang = 14;
    YongchangInfo yongchang = 25;
    HunZhiYiJiBuffInfo hun_zhi_yi_ji_info = 26;
    uint32 liqi_type_beishuizhizhan = 27;
}

message RecordDiscardTile {

    uint32 seat = 1;
    string tile = 2;
    bool is_liqi = 3;
    bool moqie = 5;
    repeated bool zhenting = 6;
    repeated TingPaiInfo tingpais = 7;
    repeated string doras = 8;
    bool is_wliqi = 9;
    repeated OptionalOperationList operations = 10;
    uint32 tile_state = 11;
    MuyuInfo muyu = 12;
    YongchangInfo yongchang = 13;
    HunZhiYiJiBuffInfo hun_zhi_yi_ji_info = 14;
    uint32 liqi_type_beishuizhizhan = 27;
}

message ActionDealTile {

    uint32 seat = 1;
    string tile = 2;
    uint32 left_tile_count = 3;
    OptionalOperationList operation = 4;
    LiQiSuccess liqi = 5;
    repeated string doras = 6;
    bool zhenting = 7;
    repeated TingPaiDiscardInfo tingpais = 8;
    uint32 tile_state = 9;
    MuyuInfo muyu = 10;
    uint32 tile_index = 11;
    HunZhiYiJiBuffInfo hun_zhi_yi_ji_info = 12;
}

message RecordDealTile {

    uint32 seat = 1;
    string tile = 2;
    uint32 left_tile_count = 3;
    LiQiSuccess liqi = 5;
    repeated string doras = 6;
    repeated bool zhenting = 7;
    OptionalOperationList operation = 8;
    uint32 tile_state = 9;
    MuyuInfo muyu = 11;
    uint32 tile_index = 12;
    HunZhiYiJiBuffInfo hun_zhi_yi_ji_info = 13;
}

message ActionFillAwaitingTiles {

    repeated string awaiting_tiles = 1;
    uint32 left_tile_count = 2;
    OptionalOperationList operation = 3;
    LiQiSuccess liqi = 4;
}

message RecordFillAwaitingTiles {

    repeated string awaiting_tiles = 1;
    uint32 left_tile_count = 2;
    OptionalOperationList operation = 3;
    LiQiSuccess liqi = 4;
}

message ActionChiPengGang {

    uint32 seat = 1;
    uint32 type = 2;
    repeated string tiles = 3;
    repeated uint32 froms = 4;
    LiQiSuccess liqi = 5;
    OptionalOperationList operation = 6;
    bool zhenting = 7;
    repeated TingPaiDiscardInfo tingpais = 8;
    repeated uint32 tile_states = 9;
    MuyuInfo muyu = 10;
    repeated int32 scores = 11;
    uint32 liqibang = 12;
    YongchangInfo yongchang = 13;
    HunZhiYiJiBuffInfo hun_zhi_yi_ji_info = 14;
}

message RecordChiPengGang {

    uint32 seat = 1;
    uint32 type = 2;
    repeated string tiles = 3;
    repeated uint32 froms = 4;
    LiQiSuccess liqi = 5;
    repeated bool zhenting = 7;
    OptionalOperationList operation = 8;
    repeated uint32 tile_states = 9;
    MuyuInfo muyu = 10;
    repeated int32 scores = 11;
    uint32 liqibang = 12;
    YongchangInfo yongchang = 13;
    HunZhiYiJiBuffInfo hun_zhi_yi_ji_info = 14;
}

message ActionGangResult {

    ChuanmaGang gang_infos = 1;
}

message RecordGangResult {

    ChuanmaGang gang_infos = 1;
}

message ActionGangResultEnd {

    ChuanmaGang gang_infos = 1;
}

message RecordGangResultEnd {

    ChuanmaGang gang_infos = 1;
}

message ActionAnGangAddGang {

    uint32 seat = 1;
    uint32 type = 2;
    string tiles = 3;
    OptionalOperationList operation = 4;
    repeated string doras = 6;
    bool zhenting = 7;
    repeated TingPaiInfo tingpais = 8;
    MuyuInfo muyu = 9;
}

message RecordAnGangAddGang {

    uint32 seat = 1;
    uint32 type = 2;
    string tiles = 3;
    repeated string doras = 6;
    repeated OptionalOperationList operations = 7;
    MuyuInfo muyu = 8;
}

message ActionBaBei {

    uint32 seat = 1;
    OptionalOperationList operation = 4;
    repeated string doras = 6;
    bool zhenting = 7;
    repeated TingPaiInfo tingpais = 8;
    bool moqie = 9;
    uint32 tile_state = 10;
    MuyuInfo muyu = 11;
}

message RecordBaBei {

    uint32 seat = 1;
    repeated string doras = 6;
    repeated OptionalOperationList operations = 7;
    bool moqie = 8;
    uint32 tile_state = 10;
    MuyuInfo muyu = 11;
}

message ActionHule {

    repeated HuleInfo hules = 1;
    repeated int32 old_scores = 2;
    repeated int32 delta_scores = 3;
    uint32 wait_timeout = 4;
    repeated int32 scores = 5;
    GameEnd gameend = 6;
    repeated string doras = 7;
    MuyuInfo muyu = 8;
    int32 baopai = 9;
    HunZhiYiJiBuffInfo hun_zhi_yi_ji_info = 10;
}

message RecordHule {

    repeated HuleInfo hules = 1;
    repeated int32 old_scores = 2;
    repeated int32 delta_scores = 3;
    uint32 wait_timeout = 4;
    repeated int32 scores = 5;
    GameEnd gameend = 6;
    repeated string doras = 7;
    MuyuInfo muyu = 8;
    int32 baopai = 9;
    HunZhiYiJiBuffInfo hun_zhi_yi_ji_info = 10;
}

message HuInfoXueZhanMid {

    uint32 seat = 1;
    uint32 hand_count = 2;
    repeated string hand = 3;
    repeated string ming = 4;
    string hu_tile = 5;
    bool zimo = 6;
    bool yiman = 7;
    uint32 count = 8;
    repeated FanInfo fans = 9;
    uint32 fu = 10;
    uint32 title_id = 11;
}

message ActionHuleXueZhanMid {

    repeated HuInfoXueZhanMid hules = 1;
    repeated int32 old_scores = 2;
    repeated int32 delta_scores = 3;
    repeated int32 scores = 5;
    repeated string doras = 7;
    MuyuInfo muyu = 8;
    LiQiSuccess liqi = 9;
    bool zhenting = 10;
}

message RecordHuleXueZhanMid {

    repeated HuInfoXueZhanMid hules = 1;
    repeated int32 old_scores = 2;
    repeated int32 delta_scores = 3;
    repeated int32 scores = 5;
    repeated string doras = 7;
    MuyuInfo muyu = 8;
    LiQiSuccess liqi = 9;
    repeated bool zhenting = 10;
}

message ActionHuleXueZhanEnd {

    repeated HuInfoXueZhanMid hules = 1;
    repeated int32 old_scores = 2;
    repeated int32 delta_scores = 3;
    repeated int32 scores = 4;
    uint32 wait_timeout = 5;
    GameEnd gameend = 6;
    repeated string doras = 7;
    MuyuInfo muyu = 8;
    repeated HuleInfo hules_history = 9;
}

message RecordHuleXueZhanEnd {

    repeated HuInfoXueZhanMid hules = 1;
    repeated int32 old_scores = 2;
    repeated int32 delta_scores = 3;
    repeated int32 scores = 4;
    uint32 wait_timeout = 5;
    GameEnd gameend = 6;
    repeated string doras = 7;
    MuyuInfo muyu = 8;
    repeated HuleInfo hules_history = 9;
}

message ActionLiuJu {

    uint32 type = 1;
    GameEnd gameend = 2;
    uint32 seat = 3;
    repeated string tiles = 4;
    LiQiSuccess liqi = 5;
    repeated string allplayertiles = 6;
    MuyuInfo muyu = 7;
    repeated HuleInfo hules_history = 9;
}

message RecordLiuJu {

    uint32 type = 1;
    GameEnd gameend = 2;
    uint32 seat = 3;
    repeated string tiles = 4;
    LiQiSuccess liqi = 5;
    repeated string allplayertiles = 6;
    MuyuInfo muyu = 7;
    repeated HuleInfo hules_history = 9;
}

message NoTilePlayerInfo {

    bool tingpai = 3;
    repeated string hand = 4;
    repeated TingPaiInfo tings = 5;
    bool already_hule = 6;
}

message NoTileScoreInfo {

    uint32 seat = 1;
    repeated int32 old_scores = 2;
    repeated int32 delta_scores = 3;
    repeated string hand = 4;
    repeated string ming = 5;
    repeated string doras = 6;
    uint32 score = 7;
    repeated int32 taxes = 8;
    repeated string lines = 9;
}

message ActionNoTile {

    bool liujumanguan = 1;
    repeated NoTilePlayerInfo players = 2;
    repeated NoTileScoreInfo scores = 3;
    bool gameend = 4;
    MuyuInfo muyu = 5;
    repeated HuleInfo hules_history = 9;
}

message RecordNoTile {

    bool liujumanguan = 1;
    repeated NoTilePlayerInfo players = 2;
    repeated NoTileScoreInfo scores = 3;
    bool gameend = 4;
    MuyuInfo muyu = 5;
    repeated HuleInfo hules_history = 9;
}

message PlayerLeaving {

    uint32 seat = 1;
}
service Route {
    rpc requestConnection (ReqRequestConnection) returns (ResRequestConnection);
    rpc requestRouteChange (ReqRequestRouteChange) returns (ResRequestRouteChange);
    rpc heartbeat (ReqHeartbeat) returns (ResHeartbeat);
}

message ReqRequestConnection {

    uint32 type = 2;
    string route_id = 3;
    uint64 timestamp = 4;
}

message ResRequestConnection {

    Error error = 1;
    uint64 timestamp = 2;
    uint32 result = 3;
}

message ReqRequestRouteChange {

    string before = 1;
    string route_id = 2;
    uint32 type = 3;
}

message ResRequestRouteChange {

    Error error = 1;
    uint32 result = 3;
}

message ReqHeartbeat {

    uint32 delay = 1;
    uint32 no_operation_counter = 2;
    uint32 platform = 3;
    uint32 network_quality = 4;
}

message ResHeartbeat {

    Error error = 1;
}