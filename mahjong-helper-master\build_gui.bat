@echo off
chcp 65001 >nul
echo 正在构建日本麻将助手 原生GUI版本...

REM 检查Go环境
echo 检查Go环境...
go version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 错误：未找到Go环境！请先安装Go
    pause
    exit /b 1
)

REM 下载依赖
echo 下载依赖包...
go mod tidy
if %ERRORLEVEL% NEQ 0 (
    echo 依赖下载失败！
    pause
    exit /b 1
)

REM 构建原生GUI版本
echo 构建原生GUI版本...
go build -ldflags "-H windowsgui -s -w" -o mahjong-helper-gui.exe .
if %ERRORLEVEL% EQU 0 (
    echo 构建成功！可执行文件：mahjong-helper-gui.exe
    echo.
    echo 使用方法：
    echo   GUI模式：直接运行 mahjong-helper-gui.exe
    echo   CLI模式：运行 mahjong-helper-gui.exe --cli
    echo.
    echo 构建完成！
) else (
    echo 构建失败！请检查错误信息
    pause
    exit /b 1
)
