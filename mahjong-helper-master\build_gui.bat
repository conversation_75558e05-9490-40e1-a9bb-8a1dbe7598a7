@echo off
chcp 65001 >nul
echo 正在构建日本麻将助手 GUI版本...

REM 检查Go环境
echo 检查Go环境...
go version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 错误：未找到Go环境！请先安装Go
    pause
    exit /b 1
)

REM 设置环境变量
echo 设置构建环境...
set CGO_ENABLED=1
set GOOS=windows
set GOARCH=amd64

REM 下载依赖
echo 下载依赖包...
go mod tidy
if %ERRORLEVEL% NEQ 0 (
    echo 依赖下载失败！
    pause
    exit /b 1
)

REM 尝试不同的构建方式
echo 构建GUI版本...

REM 首先尝试标准构建
go build -tags "gl" -ldflags "-H windowsgui -s -w" -o mahjong-helper-gui.exe .
if %ERRORLEVEL% EQU 0 (
    echo 构建成功！可执行文件：mahjong-helper-gui.exe
    echo.
    echo 使用方法：
    echo   GUI模式：直接运行 mahjong-helper-gui.exe
    echo   CLI模式：运行 mahjong-helper-gui.exe --cli
    goto :success
)

REM 如果失败，尝试禁用CGO
echo 标准构建失败，尝试禁用CGO...
set CGO_ENABLED=0
go build -ldflags "-H windowsgui -s -w" -o mahjong-helper-gui.exe .
if %ERRORLEVEL% EQU 0 (
    echo 构建成功！可执行文件：mahjong-helper-gui.exe
    echo 注意：使用了简化构建模式
    echo.
    echo 使用方法：
    echo   GUI模式：直接运行 mahjong-helper-gui.exe
    echo   CLI模式：运行 mahjong-helper-gui.exe --cli
    goto :success
)

echo 构建失败！请检查错误信息
echo 可能的解决方案：
echo 1. 安装TDM-GCC或MinGW-w64
echo 2. 确保Go版本支持CGO
echo 3. 检查防火墙设置
pause
exit /b 1

:success
echo 构建完成！
