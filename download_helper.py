#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Go助手程序下载脚本
用于下载预编译的Go助手程序
"""

import os
import requests
import sys
from pathlib import Path
import platform

def get_system_info():
    """获取系统信息"""
    system = platform.system().lower()
    arch = platform.machine().lower()
    
    if system == "windows":
        return "windows", "exe"
    elif system == "linux":
        return "linux", ""
    elif system == "darwin":
        return "darwin", ""
    else:
        return "unknown", ""

def download_helper():
    """下载Go助手程序"""
    system, ext = get_system_info()
    
    if system == "unknown":
        print("✗ 不支持的操作系统")
        return False
        
    # 这里可以添加实际的下载链接
    # 目前作为示例，实际使用时需要替换为真实的下载地址
    download_urls = {
        "windows": "https://github.com/EndlessCheng/mahjong-helper/releases/latest/download/mahjong-helper-windows.exe",
        "linux": "https://github.com/EndlessCheng/mahjong-helper/releases/latest/download/mahjong-helper-linux",
        "darwin": "https://github.com/EndlessCheng/mahjong-helper/releases/latest/download/mahjong-helper-darwin"
    }
    
    url = download_urls.get(system)
    if not url:
        print(f"✗ 没有适用于 {system} 的预编译版本")
        return False
        
    helper_dir = Path("./mahjong-helper-master")
    helper_dir.mkdir(exist_ok=True)
    
    if ext:
        output_file = helper_dir / f"mahjong-helper.{ext}"
    else:
        output_file = helper_dir / "mahjong-helper"
        
    print(f"正在下载Go助手程序...")
    print(f"下载地址: {url}")
    print(f"保存位置: {output_file}")
    
    try:
        response = requests.get(url, stream=True, timeout=30)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(output_file, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r下载进度: {progress:.1f}% ({downloaded}/{total_size} bytes)", end='')
                    else:
                        print(f"\r已下载: {downloaded} bytes", end='')
        
        print(f"\n✓ 下载完成: {output_file}")
        
        # 在Unix系统上设置执行权限
        if system in ["linux", "darwin"]:
            os.chmod(output_file, 0o755)
            print("✓ 已设置执行权限")
            
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"\n✗ 下载失败: {e}")
        return False
    except Exception as e:
        print(f"\n✗ 保存文件失败: {e}")
        return False

def main():
    """主函数"""
    print("Go助手程序下载脚本")
    print("=" * 40)
    
    system, _ = get_system_info()
    print(f"检测到系统: {platform.system()} {platform.machine()}")
    
    if system == "unknown":
        print("不支持的操作系统，请手动编译Go助手程序")
        return 1
        
    # 检查是否已存在
    helper_dir = Path("./mahjong-helper-master")
    existing_files = []
    
    for ext in ["exe", ""]:
        if ext:
            helper_file = helper_dir / f"mahjong-helper.{ext}"
        else:
            helper_file = helper_dir / "mahjong-helper"
            
        if helper_file.exists():
            existing_files.append(str(helper_file))
            
    if existing_files:
        print(f"发现已存在的助手程序: {', '.join(existing_files)}")
        choice = input("是否要重新下载？(y/N): ").strip().lower()
        if choice not in ['y', 'yes']:
            print("取消下载")
            return 0
            
    # 下载助手程序
    if download_helper():
        print("\n✅ Go助手程序下载成功！")
        print("现在可以使用GUI界面启动助手了。")
        return 0
    else:
        print("\n❌ 下载失败")
        print("\n备选方案:")
        print("1. 运行 build_helper.bat 进行本地编译（需要Go环境）")
        print("2. 手动从 GitHub 下载预编译版本")
        print("3. 联系开发者获取帮助")
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n下载被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n下载过程中发生未知错误: {e}")
        sys.exit(1)
