// Code generated by tool/liqi_test.go. DO NOT EDIT.
package api

import (
	"fmt"
	"github.com/EndlessCheng/mahjong-helper/platform/majsoul/proto/lq"
)

func (c *WebSocketClient) AuthGame(req *lq.ReqAuthGame) (resp *lq.ResAuthGame, err error) {
	respChan := make(chan *lq.ResAuthGame)
	if err = c.send(".lq.FastTest.authGame", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) BroadcastInGame(req *lq.ReqBroadcastInGame) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.FastTest.broadcastInGame", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CheckNetworkDelay(req *lq.ReqCommon) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.FastTest.checkNetworkDelay", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ConfirmNewRound(req *lq.ReqCommon) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.FastTest.confirmNewRound", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) EnterGame(req *lq.ReqCommon) (resp *lq.ResEnterGame, err error) {
	respChan := make(chan *lq.ResEnterGame)
	if err = c.send(".lq.FastTest.enterGame", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchGamePlayerState(req *lq.ReqCommon) (resp *lq.ResGamePlayerState, err error) {
	respChan := make(chan *lq.ResGamePlayerState)
	if err = c.send(".lq.FastTest.fetchGamePlayerState", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FinishSyncGame(req *lq.ReqCommon) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.FastTest.finishSyncGame", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) InputChiPengGang(req *lq.ReqChiPengGang) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.FastTest.inputChiPengGang", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) InputGameGMCommand(req *lq.ReqGMCommandInGaming) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.FastTest.inputGameGMCommand", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) InputOperation(req *lq.ReqSelfOperation) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.FastTest.inputOperation", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) SyncGame(req *lq.ReqSyncGame) (resp *lq.ResSyncGame, err error) {
	respChan := make(chan *lq.ResSyncGame)
	if err = c.send(".lq.FastTest.syncGame", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) TerminateGame(req *lq.ReqCommon) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.FastTest.terminateGame", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) AddCollectedGameRecord(req *lq.ReqAddCollectedGameRecord) (resp *lq.ResAddCollectedGameRecord, err error) {
	respChan := make(chan *lq.ResAddCollectedGameRecord)
	if err = c.send(".lq.Lobby.addCollectedGameRecord", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ApplyFriend(req *lq.ReqApplyFriend) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.applyFriend", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) BindAccount(req *lq.ReqBindAccount) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.bindAccount", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) BindEmail(req *lq.ReqBindEmail) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.bindEmail", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) BindPhoneNumber(req *lq.ReqBindPhoneNumber) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.bindPhoneNumber", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) BuyFromChestShop(req *lq.ReqBuyFromChestShop) (resp *lq.ResBuyFromChestShop, err error) {
	respChan := make(chan *lq.ResBuyFromChestShop)
	if err = c.send(".lq.Lobby.buyFromChestShop", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) BuyFromShop(req *lq.ReqBuyFromShop) (resp *lq.ResBuyFromShop, err error) {
	respChan := make(chan *lq.ResBuyFromShop)
	if err = c.send(".lq.Lobby.buyFromShop", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) BuyFromZHP(req *lq.ReqBuyFromZHP) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.buyFromZHP", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) BuyShiLian(req *lq.ReqBuyShiLian) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.buyShiLian", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CancelGooglePlayOrder(req *lq.ReqCancelGooglePlayOrder) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.cancelGooglePlayOrder", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CancelMatch(req *lq.ReqCancelMatchQueue) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.cancelMatch", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ChangeAvatar(req *lq.ReqChangeAvatar) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.changeAvatar", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ChangeCharacterSkin(req *lq.ReqChangeCharacterSkin) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.changeCharacterSkin", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ChangeCharacterView(req *lq.ReqChangeCharacterView) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.changeCharacterView", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ChangeCollectedGameRecordRemarks(req *lq.ReqChangeCollectedGameRecordRemarks) (resp *lq.ResChangeCollectedGameRecordRemarks, err error) {
	respChan := make(chan *lq.ResChangeCollectedGameRecordRemarks)
	if err = c.send(".lq.Lobby.changeCollectedGameRecordRemarks", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ChangeCommonView(req *lq.ReqChangeCommonView) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.changeCommonView", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ChangeMainCharacter(req *lq.ReqChangeMainCharacter) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.changeMainCharacter", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ClientMessage(req *lq.ReqClientMessage) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.clientMessage", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CompleteActivityFlipTask(req *lq.ReqCompleteActivityTask) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.completeActivityFlipTask", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CompleteActivityTask(req *lq.ReqCompleteActivityTask) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.completeActivityTask", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ComposeShard(req *lq.ReqComposeShard) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.composeShard", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreateAlipayAppOrder(req *lq.ReqCreateAlipayAppOrder) (resp *lq.ResCreateAlipayAppOrder, err error) {
	respChan := make(chan *lq.ResCreateAlipayAppOrder)
	if err = c.send(".lq.Lobby.createAlipayAppOrder", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreateAlipayOrder(req *lq.ReqCreateAlipayOrder) (resp *lq.ResCreateAlipayOrder, err error) {
	respChan := make(chan *lq.ResCreateAlipayOrder)
	if err = c.send(".lq.Lobby.createAlipayOrder", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreateAlipayScanOrder(req *lq.ReqCreateAlipayScanOrder) (resp *lq.ResCreateAlipayScanOrder, err error) {
	respChan := make(chan *lq.ResCreateAlipayScanOrder)
	if err = c.send(".lq.Lobby.createAlipayScanOrder", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreateBillingOrder(req *lq.ReqCreateBillingOrder) (resp *lq.ResCreateBillingOrder, err error) {
	respChan := make(chan *lq.ResCreateBillingOrder)
	if err = c.send(".lq.Lobby.createBillingOrder", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreateENAlipayOrder(req *lq.ReqCreateENAlipayOrder) (resp *lq.ResCreateENAlipayOrder, err error) {
	respChan := make(chan *lq.ResCreateENAlipayOrder)
	if err = c.send(".lq.Lobby.createENAlipayOrder", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreateENJCBOrder(req *lq.ReqCreateENJCBOrder) (resp *lq.ResCreateENJCBOrder, err error) {
	respChan := make(chan *lq.ResCreateENJCBOrder)
	if err = c.send(".lq.Lobby.createENJCBOrder", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreateENMasterCardOrder(req *lq.ReqCreateENMasterCardOrder) (resp *lq.ResCreateENMasterCardOrder, err error) {
	respChan := make(chan *lq.ResCreateENMasterCardOrder)
	if err = c.send(".lq.Lobby.createENMasterCardOrder", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreateENPaypalOrder(req *lq.ReqCreateENPaypalOrder) (resp *lq.ResCreateENPaypalOrder, err error) {
	respChan := make(chan *lq.ResCreateENPaypalOrder)
	if err = c.send(".lq.Lobby.createENPaypalOrder", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreateENVisaOrder(req *lq.ReqCreateENVisaOrder) (resp *lq.ResCreateENVisaOrder, err error) {
	respChan := make(chan *lq.ResCreateENVisaOrder)
	if err = c.send(".lq.Lobby.createENVisaOrder", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreateEmailVerifyCode(req *lq.ReqCreateEmailVerifyCode) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.createEmailVerifyCode", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreateJPAuOrder(req *lq.ReqCreateJPAuOrder) (resp *lq.ResCreateJPAuOrder, err error) {
	respChan := make(chan *lq.ResCreateJPAuOrder)
	if err = c.send(".lq.Lobby.createJPAuOrder", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreateJPCreditCardOrder(req *lq.ReqCreateJPCreditCardOrder) (resp *lq.ResCreateJPCreditCardOrder, err error) {
	respChan := make(chan *lq.ResCreateJPCreditCardOrder)
	if err = c.send(".lq.Lobby.createJPCreditCardOrder", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreateJPDocomoOrder(req *lq.ReqCreateJPDocomoOrder) (resp *lq.ResCreateJPDocomoOrder, err error) {
	respChan := make(chan *lq.ResCreateJPDocomoOrder)
	if err = c.send(".lq.Lobby.createJPDocomoOrder", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreateJPPaypalOrder(req *lq.ReqCreateJPPaypalOrder) (resp *lq.ResCreateJPPaypalOrder, err error) {
	respChan := make(chan *lq.ResCreateJPPaypalOrder)
	if err = c.send(".lq.Lobby.createJPPaypalOrder", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreateJPSoftbankOrder(req *lq.ReqCreateJPSoftbankOrder) (resp *lq.ResCreateJPSoftbankOrder, err error) {
	respChan := make(chan *lq.ResCreateJPSoftbankOrder)
	if err = c.send(".lq.Lobby.createJPSoftbankOrder", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreateJPWebMoneyOrder(req *lq.ReqCreateJPWebMoneyOrder) (resp *lq.ResCreateJPWebMoneyOrder, err error) {
	respChan := make(chan *lq.ResCreateJPWebMoneyOrder)
	if err = c.send(".lq.Lobby.createJPWebMoneyOrder", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreateNickname(req *lq.ReqCreateNickname) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.createNickname", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreatePhoneVerifyCode(req *lq.ReqCreatePhoneVerifyCode) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.createPhoneVerifyCode", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreateRoom(req *lq.ReqCreateRoom) (resp *lq.ResCreateRoom, err error) {
	respChan := make(chan *lq.ResCreateRoom)
	if err = c.send(".lq.Lobby.createRoom", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreateWechatAppOrder(req *lq.ReqCreateWechatAppOrder) (resp *lq.ResCreateWechatAppOrder, err error) {
	respChan := make(chan *lq.ResCreateWechatAppOrder)
	if err = c.send(".lq.Lobby.createWechatAppOrder", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) CreateWechatNativeOrder(req *lq.ReqCreateWechatNativeOrder) (resp *lq.ResCreateWechatNativeOrder, err error) {
	respChan := make(chan *lq.ResCreateWechatNativeOrder)
	if err = c.send(".lq.Lobby.createWechatNativeOrder", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) DeleteComment(req *lq.ReqDeleteComment) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.deleteComment", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) DeleteMail(req *lq.ReqDeleteMail) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.deleteMail", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) DoActivitySignIn(req *lq.ReqDoActivitySignIn) (resp *lq.ResDoActivitySignIn, err error) {
	respChan := make(chan *lq.ResDoActivitySignIn)
	if err = c.send(".lq.Lobby.doActivitySignIn", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) DoDailySignIn(req *lq.ReqCommon) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.doDailySignIn", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) EmailLogin(req *lq.ReqEmailLogin) (resp *lq.ResLogin, err error) {
	respChan := make(chan *lq.ResLogin)
	if err = c.send(".lq.Lobby.emailLogin", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) EnterCustomizedContest(req *lq.ReqEnterCustomizedContest) (resp *lq.ResEnterCustomizedContest, err error) {
	respChan := make(chan *lq.ResEnterCustomizedContest)
	if err = c.send(".lq.Lobby.enterCustomizedContest", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ExchangeActivityItem(req *lq.ReqExchangeActivityItem) (resp *lq.ResExchangeActivityItem, err error) {
	respChan := make(chan *lq.ResExchangeActivityItem)
	if err = c.send(".lq.Lobby.exchangeActivityItem", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ExchangeChestStone(req *lq.ReqExchangeCurrency) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.exchangeChestStone", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ExchangeCurrency(req *lq.ReqExchangeCurrency) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.exchangeCurrency", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchAccountActivityData(req *lq.ReqCommon) (resp *lq.ResAccountActivityData, err error) {
	respChan := make(chan *lq.ResAccountActivityData)
	if err = c.send(".lq.Lobby.fetchAccountActivityData", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchAccountCharacterInfo(req *lq.ReqCommon) (resp *lq.ResAccountCharacterInfo, err error) {
	respChan := make(chan *lq.ResAccountCharacterInfo)
	if err = c.send(".lq.Lobby.fetchAccountCharacterInfo", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	return
}

func (c *WebSocketClient) FetchAccountInfo(req *lq.ReqAccountInfo) (resp *lq.ResAccountInfo, err error) {
	respChan := make(chan *lq.ResAccountInfo)
	if err = c.send(".lq.Lobby.fetchAccountInfo", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchAccountSettings(req *lq.ReqCommon) (resp *lq.ResAccountSettings, err error) {
	respChan := make(chan *lq.ResAccountSettings)
	if err = c.send(".lq.Lobby.fetchAccountSettings", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchAccountState(req *lq.ReqAccountList) (resp *lq.ResAccountStates, err error) {
	respChan := make(chan *lq.ResAccountStates)
	if err = c.send(".lq.Lobby.fetchAccountState", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchAccountStatisticInfo(req *lq.ReqAccountStatisticInfo) (resp *lq.ResAccountStatisticInfo, err error) {
	respChan := make(chan *lq.ResAccountStatisticInfo)
	if err = c.send(".lq.Lobby.fetchAccountStatisticInfo", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchAchievement(req *lq.ReqCommon) (resp *lq.ResAchievement, err error) {
	respChan := make(chan *lq.ResAchievement)
	if err = c.send(".lq.Lobby.fetchAchievement", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchActivityFlipInfo(req *lq.ReqFetchActivityFlipInfo) (resp *lq.ResFetchActivityFlipInfo, err error) {
	respChan := make(chan *lq.ResFetchActivityFlipInfo)
	if err = c.send(".lq.Lobby.fetchActivityFlipInfo", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	return
}

func (c *WebSocketClient) FetchActivityList(req *lq.ReqCommon) (resp *lq.ResActivityList, err error) {
	respChan := make(chan *lq.ResActivityList)
	if err = c.send(".lq.Lobby.fetchActivityList", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchAnnouncement(req *lq.ReqCommon) (resp *lq.ResAnnouncement, err error) {
	respChan := make(chan *lq.ResAnnouncement)
	if err = c.send(".lq.Lobby.fetchAnnouncement", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchBagInfo(req *lq.ReqCommon) (resp *lq.ResBagInfo, err error) {
	respChan := make(chan *lq.ResBagInfo)
	if err = c.send(".lq.Lobby.fetchBagInfo", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchCharacterInfo(req *lq.ReqCommon) (resp *lq.ResCharacterInfo, err error) {
	respChan := make(chan *lq.ResCharacterInfo)
	if err = c.send(".lq.Lobby.fetchCharacterInfo", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchClientValue(req *lq.ReqCommon) (resp *lq.ResClientValue, err error) {
	respChan := make(chan *lq.ResClientValue)
	if err = c.send(".lq.Lobby.fetchClientValue", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	return
}

func (c *WebSocketClient) FetchCollectedGameRecordList(req *lq.ReqCommon) (resp *lq.ResCollectedGameRecordList, err error) {
	respChan := make(chan *lq.ResCollectedGameRecordList)
	if err = c.send(".lq.Lobby.fetchCollectedGameRecordList", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchCommentContent(req *lq.ReqFetchCommentContent) (resp *lq.ResFetchCommentContent, err error) {
	respChan := make(chan *lq.ResFetchCommentContent)
	if err = c.send(".lq.Lobby.fetchCommentContent", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchCommentList(req *lq.ReqFetchCommentList) (resp *lq.ResFetchCommentList, err error) {
	respChan := make(chan *lq.ResFetchCommentList)
	if err = c.send(".lq.Lobby.fetchCommentList", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchCommentSetting(req *lq.ReqCommon) (resp *lq.ResCommentSetting, err error) {
	respChan := make(chan *lq.ResCommentSetting)
	if err = c.send(".lq.Lobby.fetchCommentSetting", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchCommonView(req *lq.ReqCommon) (resp *lq.ResCommonView, err error) {
	respChan := make(chan *lq.ResCommonView)
	if err = c.send(".lq.Lobby.fetchCommonView", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchConnectionInfo(req *lq.ReqCommon) (resp *lq.ResConnectionInfo, err error) {
	respChan := make(chan *lq.ResConnectionInfo)
	if err = c.send(".lq.Lobby.fetchConnectionInfo", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchCurrentMatchInfo(req *lq.ReqCurrentMatchInfo) (resp *lq.ResCurrentMatchInfo, err error) {
	respChan := make(chan *lq.ResCurrentMatchInfo)
	if err = c.send(".lq.Lobby.fetchCurrentMatchInfo", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchCustomizedContestByContestId(req *lq.ReqFetchCustomizedContestByContestId) (resp *lq.ResFetchCustomizedContestByContestId, err error) {
	respChan := make(chan *lq.ResFetchCustomizedContestByContestId)
	if err = c.send(".lq.Lobby.fetchCustomizedContestByContestId", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchCustomizedContestExtendInfo(req *lq.ReqFetchCustomizedContestExtendInfo) (resp *lq.ResFetchCustomizedContestExtendInfo, err error) {
	respChan := make(chan *lq.ResFetchCustomizedContestExtendInfo)
	if err = c.send(".lq.Lobby.fetchCustomizedContestExtendInfo", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchCustomizedContestGameLiveList(req *lq.ReqFetchCustomizedContestGameLiveList) (resp *lq.ResFetchCustomizedContestGameLiveList, err error) {
	respChan := make(chan *lq.ResFetchCustomizedContestGameLiveList)
	if err = c.send(".lq.Lobby.fetchCustomizedContestGameLiveList", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchCustomizedContestGameRecords(req *lq.ReqFetchCustomizedContestGameRecords) (resp *lq.ResFetchCustomizedContestGameRecords, err error) {
	respChan := make(chan *lq.ResFetchCustomizedContestGameRecords)
	if err = c.send(".lq.Lobby.fetchCustomizedContestGameRecords", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchCustomizedContestList(req *lq.ReqFetchCustomizedContestList) (resp *lq.ResFetchCustomizedContestList, err error) {
	respChan := make(chan *lq.ResFetchCustomizedContestList)
	if err = c.send(".lq.Lobby.fetchCustomizedContestList", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchCustomizedContestOnlineInfo(req *lq.ReqFetchCustomizedContestOnlineInfo) (resp *lq.ResFetchCustomizedContestOnlineInfo, err error) {
	respChan := make(chan *lq.ResFetchCustomizedContestOnlineInfo)
	if err = c.send(".lq.Lobby.fetchCustomizedContestOnlineInfo", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchDailySignInInfo(req *lq.ReqCommon) (resp *lq.ResDailySignInInfo, err error) {
	respChan := make(chan *lq.ResDailySignInInfo)
	if err = c.send(".lq.Lobby.fetchDailySignInInfo", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchDailyTask(req *lq.ReqCommon) (resp *lq.ResDailyTask, err error) {
	respChan := make(chan *lq.ResDailyTask)
	if err = c.send(".lq.Lobby.fetchDailyTask", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchFriendApplyList(req *lq.ReqCommon) (resp *lq.ResFriendApplyList, err error) {
	respChan := make(chan *lq.ResFriendApplyList)
	if err = c.send(".lq.Lobby.fetchFriendApplyList", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchFriendList(req *lq.ReqCommon) (resp *lq.ResFriendList, err error) {
	respChan := make(chan *lq.ResFriendList)
	if err = c.send(".lq.Lobby.fetchFriendList", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchGameLiveInfo(req *lq.ReqGameLiveInfo) (resp *lq.ResGameLiveInfo, err error) {
	respChan := make(chan *lq.ResGameLiveInfo)
	if err = c.send(".lq.Lobby.fetchGameLiveInfo", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchGameLiveLeftSegment(req *lq.ReqGameLiveLeftSegment) (resp *lq.ResGameLiveLeftSegment, err error) {
	respChan := make(chan *lq.ResGameLiveLeftSegment)
	if err = c.send(".lq.Lobby.fetchGameLiveLeftSegment", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchGameLiveList(req *lq.ReqGameLiveList) (resp *lq.ResGameLiveList, err error) {
	respChan := make(chan *lq.ResGameLiveList)
	if err = c.send(".lq.Lobby.fetchGameLiveList", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchGameRecord(req *lq.ReqGameRecord) (resp *lq.ResGameRecord, err error) {
	respChan := make(chan *lq.ResGameRecord)
	if err = c.send(".lq.Lobby.fetchGameRecord", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchGameRecordList(req *lq.ReqGameRecordList) (resp *lq.ResGameRecordList, err error) {
	respChan := make(chan *lq.ResGameRecordList)
	if err = c.send(".lq.Lobby.fetchGameRecordList", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchGameRecordsDetail(req *lq.ReqGameRecordsDetail) (resp *lq.ResGameRecordsDetail, err error) {
	respChan := make(chan *lq.ResGameRecordsDetail)
	if err = c.send(".lq.Lobby.fetchGameRecordsDetail", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchIDCardInfo(req *lq.ReqCommon) (resp *lq.ResIDCardInfo, err error) {
	respChan := make(chan *lq.ResIDCardInfo)
	if err = c.send(".lq.Lobby.fetchIDCardInfo", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchLevelLeaderboard(req *lq.ReqLevelLeaderboard) (resp *lq.ResLevelLeaderboard, err error) {
	respChan := make(chan *lq.ResLevelLeaderboard)
	if err = c.send(".lq.Lobby.fetchLevelLeaderboard", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchMailInfo(req *lq.ReqCommon) (resp *lq.ResMailInfo, err error) {
	respChan := make(chan *lq.ResMailInfo)
	if err = c.send(".lq.Lobby.fetchMailInfo", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchMisc(req *lq.ReqCommon) (resp *lq.ResMisc, err error) {
	respChan := make(chan *lq.ResMisc)
	if err = c.send(".lq.Lobby.fetchMisc", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchModNicknameTime(req *lq.ReqCommon) (resp *lq.ResModNicknameTime, err error) {
	respChan := make(chan *lq.ResModNicknameTime)
	if err = c.send(".lq.Lobby.fetchModNicknameTime", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	return
}

func (c *WebSocketClient) FetchMonthTicketInfo(req *lq.ReqCommon) (resp *lq.ResMonthTicketInfo, err error) {
	respChan := make(chan *lq.ResMonthTicketInfo)
	if err = c.send(".lq.Lobby.fetchMonthTicketInfo", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	return
}

func (c *WebSocketClient) FetchMultiAccountBrief(req *lq.ReqMultiAccountId) (resp *lq.ResMultiAccountBrief, err error) {
	respChan := make(chan *lq.ResMultiAccountBrief)
	if err = c.send(".lq.Lobby.fetchMultiAccountBrief", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchPlatformProducts(req *lq.ReqPlatformBillingProducts) (resp *lq.ResPlatformBillingProducts, err error) {
	respChan := make(chan *lq.ResPlatformBillingProducts)
	if err = c.send(".lq.Lobby.fetchPlatformProducts", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchRankPointLeaderboard(req *lq.ReqFetchRankPointLeaderboard) (resp *lq.ResFetchRankPointLeaderboard, err error) {
	respChan := make(chan *lq.ResFetchRankPointLeaderboard)
	if err = c.send(".lq.Lobby.fetchRankPointLeaderboard", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchReviveCoinInfo(req *lq.ReqCommon) (resp *lq.ResReviveCoinInfo, err error) {
	respChan := make(chan *lq.ResReviveCoinInfo)
	if err = c.send(".lq.Lobby.fetchReviveCoinInfo", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchRollingNotice(req *lq.ReqCommon) (resp *lq.ReqRollingNotice, err error) {
	respChan := make(chan *lq.ReqRollingNotice)
	if err = c.send(".lq.Lobby.fetchRollingNotice", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	return
}

func (c *WebSocketClient) FetchRoom(req *lq.ReqCommon) (resp *lq.ResSelfRoom, err error) {
	respChan := make(chan *lq.ResSelfRoom)
	if err = c.send(".lq.Lobby.fetchRoom", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchServerSettings(req *lq.ReqCommon) (resp *lq.ResServerSettings, err error) {
	respChan := make(chan *lq.ResServerSettings)
	if err = c.send(".lq.Lobby.fetchServerSettings", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	return
}

func (c *WebSocketClient) FetchServerTime(req *lq.ReqCommon) (resp *lq.ResServerTime, err error) {
	respChan := make(chan *lq.ResServerTime)
	if err = c.send(".lq.Lobby.fetchServerTime", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	return
}

func (c *WebSocketClient) FetchShopInfo(req *lq.ReqCommon) (resp *lq.ResShopInfo, err error) {
	respChan := make(chan *lq.ResShopInfo)
	if err = c.send(".lq.Lobby.fetchShopInfo", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchTitleList(req *lq.ReqCommon) (resp *lq.ResTitleList, err error) {
	respChan := make(chan *lq.ResTitleList)
	if err = c.send(".lq.Lobby.fetchTitleList", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FetchVipReward(req *lq.ReqCommon) (resp *lq.ResVipReward, err error) {
	respChan := make(chan *lq.ResVipReward)
	if err = c.send(".lq.Lobby.fetchVipReward", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) FollowCustomizedContest(req *lq.ReqTargetCustomizedContest) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.followCustomizedContest", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) GainAccumulatedPointActivityReward(req *lq.ReqGainAccumulatedPointActivityReward) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.gainAccumulatedPointActivityReward", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) GainRankPointReward(req *lq.ReqGainRankPointReward) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.gainRankPointReward", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) GainReviveCoin(req *lq.ReqCommon) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.gainReviveCoin", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) GainVipReward(req *lq.ReqGainVipReward) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.gainVipReward", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) GameMasterCommand(req *lq.ReqGMCommand) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.gameMasterCommand", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) GoNextShiLian(req *lq.ReqCommon) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.goNextShiLian", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) HandleFriendApply(req *lq.ReqHandleFriendApply) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.handleFriendApply", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) Heatbeat(req *lq.ReqHeatBeat) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.heatbeat", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) JoinCustomizedContestChatRoom(req *lq.ReqJoinCustomizedContestChatRoom) (resp *lq.ResJoinCustomizedContestChatRoom, err error) {
	respChan := make(chan *lq.ResJoinCustomizedContestChatRoom)
	if err = c.send(".lq.Lobby.joinCustomizedContestChatRoom", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) JoinRoom(req *lq.ReqJoinRoom) (resp *lq.ResJoinRoom, err error) {
	respChan := make(chan *lq.ResJoinRoom)
	if err = c.send(".lq.Lobby.joinRoom", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) KickPlayer(req *lq.ReqRoomKick) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.kickPlayer", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) LeaveComment(req *lq.ReqLeaveComment) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.leaveComment", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) LeaveCustomizedContest(req *lq.ReqCommon) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.leaveCustomizedContest", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) LeaveCustomizedContestChatRoom(req *lq.ReqCommon) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.leaveCustomizedContestChatRoom", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) LeaveRoom(req *lq.ReqCommon) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.leaveRoom", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) Login(req *lq.ReqLogin) (resp *lq.ResLogin, err error) {
	respChan := make(chan *lq.ResLogin)
	if err = c.send(".lq.Lobby.login", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) LoginBeat(req *lq.ReqLoginBeat) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.loginBeat", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) Logout(req *lq.ReqLogout) (resp *lq.ResLogout, err error) {
	respChan := make(chan *lq.ResLogout)
	if err = c.send(".lq.Lobby.logout", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) MatchGame(req *lq.ReqJoinMatchQueue) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.matchGame", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) MatchShiLian(req *lq.ReqCommon) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.matchShiLian", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ModifyBirthday(req *lq.ReqModifyBirthday) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.modifyBirthday", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ModifyNickname(req *lq.ReqModifyNickname) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.modifyNickname", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ModifyPassword(req *lq.ReqModifyPassword) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.modifyPassword", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ModifyRoom(req *lq.ReqModifyRoom) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.modifyRoom", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ModifySignature(req *lq.ReqModifySignature) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.modifySignature", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) Oauth2Auth(req *lq.ReqOauth2Auth) (resp *lq.ResOauth2Auth, err error) {
	respChan := make(chan *lq.ResOauth2Auth)
	if err = c.send(".lq.Lobby.oauth2Auth", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) Oauth2Check(req *lq.ReqOauth2Check) (resp *lq.ResOauth2Check, err error) {
	respChan := make(chan *lq.ResOauth2Check)
	if err = c.send(".lq.Lobby.oauth2Check", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) Oauth2Login(req *lq.ReqOauth2Login) (resp *lq.ResLogin, err error) {
	respChan := make(chan *lq.ResLogin)
	if err = c.send(".lq.Lobby.oauth2Login", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) Oauth2Signup(req *lq.ReqOauth2Signup) (resp *lq.ResOauth2Signup, err error) {
	respChan := make(chan *lq.ResOauth2Signup)
	if err = c.send(".lq.Lobby.oauth2Signup", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) OpenChest(req *lq.ReqOpenChest) (resp *lq.ResOpenChest, err error) {
	respChan := make(chan *lq.ResOpenChest)
	if err = c.send(".lq.Lobby.openChest", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) OpenManualItem(req *lq.ReqOpenManualItem) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.openManualItem", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) OpenRandomRewardItem(req *lq.ReqOpenRandomRewardItem) (resp *lq.ResOpenRandomRewardItem, err error) {
	respChan := make(chan *lq.ResOpenRandomRewardItem)
	if err = c.send(".lq.Lobby.openRandomRewardItem", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) PayMonthTicket(req *lq.ReqPayMonthTicket) (resp *lq.ResPayMonthTicket, err error) {
	respChan := make(chan *lq.ResPayMonthTicket)
	if err = c.send(".lq.Lobby.payMonthTicket", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ReadAnnouncement(req *lq.ReqReadAnnouncement) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.readAnnouncement", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ReadMail(req *lq.ReqReadMail) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.readMail", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ReadyPlay(req *lq.ReqRoomReady) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.readyPlay", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) RecieveActivityFlipTask(req *lq.ReqRecieveActivityFlipTask) (resp *lq.ResRecieveActivityFlipTask, err error) {
	respChan := make(chan *lq.ResRecieveActivityFlipTask)
	if err = c.send(".lq.Lobby.recieveActivityFlipTask", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	return
}

func (c *WebSocketClient) RefreshDailyTask(req *lq.ReqRefreshDailyTask) (resp *lq.ResRefreshDailyTask, err error) {
	respChan := make(chan *lq.ResRefreshDailyTask)
	if err = c.send(".lq.Lobby.refreshDailyTask", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) RefreshZHPShop(req *lq.ReqCommon) (resp *lq.ResRefreshZHPShop, err error) {
	respChan := make(chan *lq.ResRefreshZHPShop)
	if err = c.send(".lq.Lobby.refreshZHPShop", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) RemoveCollectedGameRecord(req *lq.ReqRemoveCollectedGameRecord) (resp *lq.ResRemoveCollectedGameRecord, err error) {
	respChan := make(chan *lq.ResRemoveCollectedGameRecord)
	if err = c.send(".lq.Lobby.removeCollectedGameRecord", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) RemoveFriend(req *lq.ReqRemoveFriend) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.removeFriend", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) SayChatMessage(req *lq.ReqSayChatMessage) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.sayChatMessage", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) SearchAccountById(req *lq.ReqSearchAccountById) (resp *lq.ResSearchAccountById, err error) {
	respChan := make(chan *lq.ResSearchAccountById)
	if err = c.send(".lq.Lobby.searchAccountById", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) SearchAccountByPattern(req *lq.ReqSearchAccountByPattern) (resp *lq.ResSearchAccountByPattern, err error) {
	respChan := make(chan *lq.ResSearchAccountByPattern)
	if err = c.send(".lq.Lobby.searchAccountByPattern", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) SellItem(req *lq.ReqSellItem) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.sellItem", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) SendClientMessage(req *lq.ReqSendClientMessage) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.sendClientMessage", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) SendGiftToCharacter(req *lq.ReqSendGiftToCharacter) (resp *lq.ResSendGiftToCharacter, err error) {
	respChan := make(chan *lq.ResSendGiftToCharacter)
	if err = c.send(".lq.Lobby.sendGiftToCharacter", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) ShopPurchase(req *lq.ReqShopPurchase) (resp *lq.ResShopPurchase, err error) {
	respChan := make(chan *lq.ResShopPurchase)
	if err = c.send(".lq.Lobby.shopPurchase", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) Signup(req *lq.ReqSignupAccount) (resp *lq.ResSignupAccount, err error) {
	respChan := make(chan *lq.ResSignupAccount)
	if err = c.send(".lq.Lobby.signup", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) SolveGooglePlayOrder(req *lq.ReqSolveGooglePlayOrder) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.solveGooglePlayOrder", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) StartCustomizedContest(req *lq.ReqStartCustomizedContest) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.startCustomizedContest", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) StartRoom(req *lq.ReqRoomStart) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.startRoom", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) StopCustomizedContest(req *lq.ReqCommon) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.stopCustomizedContest", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) TakeAttachmentFromMail(req *lq.ReqTakeAttachment) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.takeAttachmentFromMail", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) UnfollowCustomizedContest(req *lq.ReqTargetCustomizedContest) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.unfollowCustomizedContest", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) UpdateAccountSettings(req *lq.ReqUpdateAccountSettings) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.updateAccountSettings", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) UpdateClientValue(req *lq.ReqUpdateClientValue) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.updateClientValue", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) UpdateCommentSetting(req *lq.ReqUpdateCommentSetting) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.updateCommentSetting", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) UpdateIDCardInfo(req *lq.ReqUpdateIDCardInfo) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.updateIDCardInfo", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) UpdateReadComment(req *lq.ReqUpdateReadComment) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.updateReadComment", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) UpgradeCharacter(req *lq.ReqUpgradeCharacter) (resp *lq.ResUpgradeCharacter, err error) {
	respChan := make(chan *lq.ResUpgradeCharacter)
	if err = c.send(".lq.Lobby.upgradeCharacter", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) UseBagItem(req *lq.ReqUseBagItem) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.useBagItem", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) UseGiftCode(req *lq.ReqUseGiftCode) (resp *lq.ResUseGiftCode, err error) {
	respChan := make(chan *lq.ResUseGiftCode)
	if err = c.send(".lq.Lobby.useGiftCode", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) UseTitle(req *lq.ReqUseTitle) (resp *lq.ResCommon, err error) {
	respChan := make(chan *lq.ResCommon)
	if err = c.send(".lq.Lobby.useTitle", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}

func (c *WebSocketClient) VerfifyCodeForSecure(req *lq.ReqVerifyCodeForSecure) (resp *lq.ResVerfiyCodeForSecure, err error) {
	respChan := make(chan *lq.ResVerfiyCodeForSecure)
	if err = c.send(".lq.Lobby.verfifyCodeForSecure", req, respChan); err != nil {
		return
	}
	resp = <-respChan
	if resp == nil {
		return nil, fmt.Errorf("empty response")
	}
	if resp.Error != nil {
		err = fmt.Errorf("majsoul error: %s", resp.Error.String())
	}
	return
}
